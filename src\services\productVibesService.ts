import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

// Emoji types mapping
export const EMOJI_TYPES = {
  love: '💖',
  fire: '🔥',
  star_eyes: '🤩',
  neutral: '😐',
  thumbs_down: '👎'
} as const;

export type EmojiType = keyof typeof EMOJI_TYPES;

// Interface for vibe data
export interface ProductVibe {
  id: string;
  productId: string;
  userId: string;
  emojiType: EmojiType;
  createdAt: string;
  updatedAt: string;
}

// Interface for vibe summary
export interface ProductVibesSummary {
  productId: string;
  totalVibes: number;
  loveCount: number;
  fireCount: number;
  starEyesCount: number;
  neutralCount: number;
  thumbsDownCount: number;
}

// Interface for user's vibe on a product
export interface UserProductVibe {
  productId: string;
  emojiType: EmojiType | null;
}

// Map Supabase vibe to frontend format
const mapSupabaseVibeToFrontend = (vibe: any): ProductVibe => ({
  id: vibe.id,
  productId: vibe.product_id,
  userId: vibe.user_id,
  emojiType: vibe.emoji_type as EmojiType,
  createdAt: vibe.created_at,
  updatedAt: vibe.updated_at
});

/**
 * Get vibes summary for a product
 */
export const getProductVibesSummary = async (productId: string): Promise<ProductVibesSummary> => {
  try {
    const { data, error } = await supabase
      .from('product_vibes_summary')
      .select('*')
      .eq('product_id', productId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching vibes summary:', error);
      // Return empty summary if view doesn't exist or has error
      return {
        productId,
        totalVibes: 0,
        loveCount: 0,
        fireCount: 0,
        starEyesCount: 0,
        neutralCount: 0,
        thumbsDownCount: 0
      };
    }

    if (!data) {
      return {
        productId,
        totalVibes: 0,
        loveCount: 0,
        fireCount: 0,
        starEyesCount: 0,
        neutralCount: 0,
        thumbsDownCount: 0
      };
    }

    return {
      productId: data.product_id,
      totalVibes: data.total_vibes || 0,
      loveCount: data.love_count || 0,
      fireCount: data.fire_count || 0,
      starEyesCount: data.star_eyes_count || 0,
      neutralCount: data.neutral_count || 0,
      thumbsDownCount: data.thumbs_down_count || 0
    };
  } catch (error: any) {
    console.error(`Error getting vibes summary for product ${productId}:`, error);
    return {
      productId,
      totalVibes: 0,
      loveCount: 0,
      fireCount: 0,
      starEyesCount: 0,
      neutralCount: 0,
      thumbsDownCount: 0
    };
  }
};

/**
 * Get user's vibe for a specific product
 */
export const getUserVibeForProduct = async (productId: string, userId: string): Promise<EmojiType | null> => {
  try {
    const { data, error } = await supabase
      .rpc('get_user_vibe', {
        p_product_id: productId,
        p_user_id: userId
      });

    if (error) {
      console.error('Error getting user vibe:', error);
      return null;
    }

    return data as EmojiType | null;
  } catch (error: any) {
    console.error(`Error getting user vibe for product ${productId}:`, error);
    return null;
  }
};

/**
 * Toggle user's vibe for a product (add, update, or remove)
 */
export const toggleUserVibe = async (productId: string, emojiType: EmojiType): Promise<boolean> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'You must be logged in to react to products',
        variant: 'destructive',
      });
      return false;
    }

    const { data, error } = await supabase
      .rpc('toggle_user_vibe', {
        p_product_id: productId,
        p_user_id: user.id,
        p_emoji_type: emojiType
      });

    if (error) {
      console.error('Error toggling vibe:', error);
      toast({
        title: 'Error',
        description: 'Failed to update your reaction. Please try again.',
        variant: 'destructive',
      });
      return false;
    }

    return data; // Returns true if vibe was added/updated, false if removed
  } catch (error: any) {
    console.error(`Error toggling vibe for product ${productId}:`, error);
    toast({
      title: 'Error',
      description: 'An unexpected error occurred. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Get all vibes for a product (for admin purposes)
 */
export const getProductVibes = async (productId: string): Promise<ProductVibe[]> => {
  try {
    const { data, error } = await supabase
      .from('vibes')
      .select('*')
      .eq('product_id', productId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching product vibes:', error);
      return [];
    }

    return (data || []).map(mapSupabaseVibeToFrontend);
  } catch (error: any) {
    console.error(`Error getting vibes for product ${productId}:`, error);
    return [];
  }
};

/**
 * Subscribe to real-time vibe changes for a product
 */
export const subscribeToProductVibes = (
  productId: string,
  callback: (summary: ProductVibesSummary) => void
) => {
  const subscription = supabase
    .channel(`product-vibes-${productId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'vibes',
        filter: `product_id=eq.${productId}`
      },
      async () => {
        // Fetch updated summary when vibes change
        const summary = await getProductVibesSummary(productId);
        callback(summary);
      }
    )
    .subscribe();

  return subscription;
};

/**
 * Get vibes summary for multiple products (batch operation)
 */
export const getMultipleProductVibesSummary = async (productIds: string[]): Promise<Record<string, ProductVibesSummary>> => {
  try {
    const { data, error } = await supabase
      .from('product_vibes_summary')
      .select('*')
      .in('product_id', productIds);

    if (error) {
      console.error('Error fetching multiple vibes summaries:', error);
      // Return empty summaries for all products
      const result: Record<string, ProductVibesSummary> = {};
      productIds.forEach(id => {
        result[id] = {
          productId: id,
          totalVibes: 0,
          loveCount: 0,
          fireCount: 0,
          starEyesCount: 0,
          neutralCount: 0,
          thumbsDownCount: 0
        };
      });
      return result;
    }

    const result: Record<string, ProductVibesSummary> = {};
    
    // Initialize all products with empty summaries
    productIds.forEach(id => {
      result[id] = {
        productId: id,
        totalVibes: 0,
        loveCount: 0,
        fireCount: 0,
        starEyesCount: 0,
        neutralCount: 0,
        thumbsDownCount: 0
      };
    });

    // Update with actual data
    (data || []).forEach(item => {
      result[item.product_id] = {
        productId: item.product_id,
        totalVibes: item.total_vibes || 0,
        loveCount: item.love_count || 0,
        fireCount: item.fire_count || 0,
        starEyesCount: item.star_eyes_count || 0,
        neutralCount: item.neutral_count || 0,
        thumbsDownCount: item.thumbs_down_count || 0
      };
    });

    return result;
  } catch (error: any) {
    console.error('Error getting multiple vibes summaries:', error);
    // Return empty summaries for all products
    const result: Record<string, ProductVibesSummary> = {};
    productIds.forEach(id => {
      result[id] = {
        productId: id,
        totalVibes: 0,
        loveCount: 0,
        fireCount: 0,
        starEyesCount: 0,
        neutralCount: 0,
        thumbsDownCount: 0
      };
    });
    return result;
  }
};
