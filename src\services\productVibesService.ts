import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

// Emoji types mapping
export const EMOJI_TYPES = {
  love: '💖',
  fire: '🔥',
  star_eyes: '🤩',
  neutral: '😐',
  thumbs_down: '👎'
} as const;

export type EmojiType = keyof typeof EMOJI_TYPES;

// Interface for vibe data
export interface ProductVibe {
  id: string;
  productId: string;
  userId: string;
  emojiType: EmojiType;
  createdAt: string;
  updatedAt: string;
}

// Interface for vibe summary
export interface ProductVibesSummary {
  productId: string;
  totalVibes: number;
  loveCount: number;
  fireCount: number;
  starEyesCount: number;
  neutralCount: number;
  thumbsDownCount: number;
}

// Interface for user's vibe on a product
export interface UserProductVibe {
  productId: string;
  emojiType: EmojiType | null;
}

// Map Supabase vibe to frontend format
const mapSupabaseVibeToFrontend = (vibe: any): ProductVibe => ({
  id: vibe.id,
  productId: vibe.product_id,
  userId: vibe.user_id,
  emojiType: vibe.emoji_type as EmojiType,
  createdAt: vibe.created_at,
  updatedAt: vibe.updated_at
});

/**
 * Get vibes summary for a product
 */
export const getProductVibesSummary = async (productId: string): Promise<ProductVibesSummary> => {
  try {
    // First try to get from the view
    const { data: viewData, error: viewError } = await supabase
      .from('product_vibes_summary')
      .select('*')
      .eq('product_id', productId)
      .single();

    // If view exists and has data, use it
    if (!viewError && viewData) {
      return {
        productId: viewData.product_id,
        totalVibes: viewData.total_vibes || 0,
        loveCount: viewData.love_count || 0,
        fireCount: viewData.fire_count || 0,
        starEyesCount: viewData.star_eyes_count || 0,
        neutralCount: viewData.neutral_count || 0,
        thumbsDownCount: viewData.thumbs_down_count || 0
      };
    }

    // If view doesn't exist or has no data, calculate manually from vibes table
    const { data: vibesData, error: vibesError } = await supabase
      .from('vibes')
      .select('emoji_type')
      .eq('product_id', productId);

    if (vibesError) {
      console.error('Error fetching vibes data:', vibesError);
      // Return empty summary if vibes table also doesn't exist
      return {
        productId,
        totalVibes: 0,
        loveCount: 0,
        fireCount: 0,
        starEyesCount: 0,
        neutralCount: 0,
        thumbsDownCount: 0
      };
    }

    // Calculate counts manually
    const vibes = vibesData || [];
    const summary = {
      productId,
      totalVibes: vibes.length,
      loveCount: vibes.filter(v => v.emoji_type === 'love').length,
      fireCount: vibes.filter(v => v.emoji_type === 'fire').length,
      starEyesCount: vibes.filter(v => v.emoji_type === 'star_eyes').length,
      neutralCount: vibes.filter(v => v.emoji_type === 'neutral').length,
      thumbsDownCount: vibes.filter(v => v.emoji_type === 'thumbs_down').length
    };

    return summary;
  } catch (error: any) {
    console.error(`Error getting vibes summary for product ${productId}:`, error);
    return {
      productId,
      totalVibes: 0,
      loveCount: 0,
      fireCount: 0,
      starEyesCount: 0,
      neutralCount: 0,
      thumbsDownCount: 0
    };
  }
};

/**
 * Get user's vibe for a specific product
 */
export const getUserVibeForProduct = async (productId: string, userId: string): Promise<EmojiType | null> => {
  try {
    // First try using the RPC function
    const { data, error } = await supabase
      .rpc('get_user_vibe', {
        p_product_id: productId,
        p_user_id: userId
      });

    if (!error) {
      return data as EmojiType | null;
    }

    // If RPC function doesn't exist, query directly
    const { data: vibeData, error: vibeError } = await supabase
      .from('vibes')
      .select('emoji_type')
      .eq('product_id', productId)
      .eq('user_id', userId)
      .single();

    if (vibeError && vibeError.code !== 'PGRST116') {
      console.error('Error getting user vibe:', vibeError);
      return null;
    }

    return vibeData?.emoji_type as EmojiType || null;
  } catch (error: any) {
    console.error(`Error getting user vibe for product ${productId}:`, error);
    return null;
  }
};

/**
 * Toggle user's vibe for a product (add, update, or remove)
 */
export const toggleUserVibe = async (productId: string, emojiType: EmojiType): Promise<boolean> => {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'You must be logged in to react to products',
        variant: 'destructive',
      });
      return false;
    }

    // First try using the RPC function
    const { data, error } = await supabase
      .rpc('toggle_user_vibe', {
        p_product_id: productId,
        p_user_id: user.id,
        p_emoji_type: emojiType
      });

    if (!error) {
      return data; // Returns true if vibe was added/updated, false if removed
    }

    // If RPC function doesn't exist, handle manually
    console.log('RPC function not available, handling manually');

    // Check if user already has a vibe for this product
    const { data: existingVibe, error: checkError } = await supabase
      .from('vibes')
      .select('emoji_type')
      .eq('product_id', productId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing vibe:', checkError);
      toast({
        title: 'Error',
        description: 'Failed to check your current reaction. Please try again.',
        variant: 'destructive',
      });
      return false;
    }

    if (existingVibe) {
      if (existingVibe.emoji_type === emojiType) {
        // Same emoji clicked, remove the vibe
        const { error: deleteError } = await supabase
          .from('vibes')
          .delete()
          .eq('product_id', productId)
          .eq('user_id', user.id);

        if (deleteError) {
          console.error('Error removing vibe:', deleteError);
          toast({
            title: 'Error',
            description: 'Failed to remove your reaction. Please try again.',
            variant: 'destructive',
          });
          return false;
        }
        return false; // Vibe removed
      } else {
        // Different emoji clicked, update the vibe
        const { error: updateError } = await supabase
          .from('vibes')
          .update({ emoji_type: emojiType, updated_at: new Date().toISOString() })
          .eq('product_id', productId)
          .eq('user_id', user.id);

        if (updateError) {
          console.error('Error updating vibe:', updateError);
          toast({
            title: 'Error',
            description: 'Failed to update your reaction. Please try again.',
            variant: 'destructive',
          });
          return false;
        }
        return true; // Vibe updated
      }
    } else {
      // No existing vibe, create new one
      const { error: insertError } = await supabase
        .from('vibes')
        .insert({
          product_id: productId,
          user_id: user.id,
          emoji_type: emojiType
        });

      if (insertError) {
        console.error('Error creating vibe:', insertError);
        toast({
          title: 'Error',
          description: 'Failed to add your reaction. Please try again.',
          variant: 'destructive',
        });
        return false;
      }
      return true; // Vibe added
    }
  } catch (error: any) {
    console.error(`Error toggling vibe for product ${productId}:`, error);
    toast({
      title: 'Error',
      description: 'An unexpected error occurred. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Get all vibes for a product (for admin purposes)
 */
export const getProductVibes = async (productId: string): Promise<ProductVibe[]> => {
  try {
    const { data, error } = await supabase
      .from('vibes')
      .select('*')
      .eq('product_id', productId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching product vibes:', error);
      return [];
    }

    return (data || []).map(mapSupabaseVibeToFrontend);
  } catch (error: any) {
    console.error(`Error getting vibes for product ${productId}:`, error);
    return [];
  }
};

/**
 * Subscribe to real-time vibe changes for a product
 */
export const subscribeToProductVibes = (
  productId: string,
  callback: (summary: ProductVibesSummary) => void
) => {
  const subscription = supabase
    .channel(`product-vibes-${productId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'vibes',
        filter: `product_id=eq.${productId}`
      },
      async () => {
        // Fetch updated summary when vibes change
        const summary = await getProductVibesSummary(productId);
        callback(summary);
      }
    )
    .subscribe();

  return subscription;
};

/**
 * Get vibes summary for multiple products (batch operation)
 */
export const getMultipleProductVibesSummary = async (productIds: string[]): Promise<Record<string, ProductVibesSummary>> => {
  try {
    // First try to get from the view
    const { data: viewData, error: viewError } = await supabase
      .from('product_vibes_summary')
      .select('*')
      .in('product_id', productIds);

    // If view works, use it
    if (!viewError && viewData) {
      const result: Record<string, ProductVibesSummary> = {};

      // Initialize all products with empty summaries
      productIds.forEach(id => {
        result[id] = {
          productId: id,
          totalVibes: 0,
          loveCount: 0,
          fireCount: 0,
          starEyesCount: 0,
          neutralCount: 0,
          thumbsDownCount: 0
        };
      });

      // Update with actual data
      viewData.forEach(item => {
        result[item.product_id] = {
          productId: item.product_id,
          totalVibes: item.total_vibes || 0,
          loveCount: item.love_count || 0,
          fireCount: item.fire_count || 0,
          starEyesCount: item.star_eyes_count || 0,
          neutralCount: item.neutral_count || 0,
          thumbsDownCount: item.thumbs_down_count || 0
        };
      });

      return result;
    }

    // If view doesn't exist, calculate manually from vibes table
    const { data: vibesData, error: vibesError } = await supabase
      .from('vibes')
      .select('product_id, emoji_type')
      .in('product_id', productIds);

    const result: Record<string, ProductVibesSummary> = {};

    // Initialize all products with empty summaries
    productIds.forEach(id => {
      result[id] = {
        productId: id,
        totalVibes: 0,
        loveCount: 0,
        fireCount: 0,
        starEyesCount: 0,
        neutralCount: 0,
        thumbsDownCount: 0
      };
    });

    if (!vibesError && vibesData) {
      // Calculate counts manually for each product
      vibesData.forEach(vibe => {
        const productId = vibe.product_id;
        if (result[productId]) {
          result[productId].totalVibes++;
          switch (vibe.emoji_type) {
            case 'love':
              result[productId].loveCount++;
              break;
            case 'fire':
              result[productId].fireCount++;
              break;
            case 'star_eyes':
              result[productId].starEyesCount++;
              break;
            case 'neutral':
              result[productId].neutralCount++;
              break;
            case 'thumbs_down':
              result[productId].thumbsDownCount++;
              break;
          }
        }
      });
    }

    return result;
  } catch (error: any) {
    console.error('Error getting multiple vibes summaries:', error);
    // Return empty summaries for all products
    const result: Record<string, ProductVibesSummary> = {};
    productIds.forEach(id => {
      result[id] = {
        productId: id,
        totalVibes: 0,
        loveCount: 0,
        fireCount: 0,
        starEyesCount: 0,
        neutralCount: 0,
        thumbsDownCount: 0
      };
    });
    return result;
  }
};
