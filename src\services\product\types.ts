/**
 * Product Types
 *
 * This module provides type definitions for product-related data.
 */
import { BaseEntity, ProductStatus } from '@/types/common';

// Import Category directly from the dedicated file
import type { Category } from '@/types/category';
// Re-export it for use in other files
export type { Category };

/**
 * Product image interface
 */
export interface ProductImage {
  id: string;
  product_id: string;
  image_url: string;
  is_primary: boolean;
  display_order: number;
}

/**
 * Supabase product interface (database schema)
 */
export interface SupabaseProduct extends BaseEntity {
  name: string;
  description?: string;
  price: number;
  sale_price?: number;
  is_sale?: boolean;
  is_new?: boolean;
  is_featured?: boolean;
  category_id: string;
  status: ProductStatus;
  stock: number;
  sku?: string;
  customization_available?: boolean;
  specifications?: Record<string, string>;
  // Shipping fields
  shipping_fee_bangalore?: number;
  shipping_fee_outside_bangalore?: number;
  free_shipping_threshold?: number;
  shipping_notes?: string;
  // Direct rating columns
  rating?: number;
  review_count?: number;
  // Joined fields
  category?: Category;
  images?: ProductImage[];
  // Rating summary from the view
  rating_summary?: {
    average_rating: string;
    review_count: string;
  };
}

/**
 * Frontend product interface (client-side representation)
 */
export interface FrontendProduct extends BaseEntity {
  name: string;
  description: string;
  price: number;
  salePrice?: number | null;
  isSale?: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
  image: string;
  images: string[];
  category: string;
  status: ProductStatus;
  stock: number;
  stockStatus: "in_stock" | "out_of_stock" | "low_stock"; // Add stock status
  sku: string;
  specifications?: Record<string, string>;
  customizationAvailable?: boolean;
  // Shipping fields
  shippingFeeBangalore?: number;
  shippingFeeOutsideBangalore?: number;
  freeShippingThreshold?: number;
  shippingNotes?: string;
  rating: number;
  reviewCount: number;
}

/**
 * Product creation/update input interface
 */
export interface ProductInput {
  id?: string;
  name: string;
  description?: string;
  price: number;
  salePrice?: number | null;
  isSale?: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
  image?: string;
  images?: string[];
  category: string;
  status: ProductStatus;
  stock: number;
  sku?: string;
  specifications?: Record<string, string>;
  customizationAvailable?: boolean;
  // Shipping fields
  shippingFeeBangalore?: number;
  shippingFeeOutsideBangalore?: number;
  freeShippingThreshold?: number;
  shippingNotes?: string;
}

/**
 * Product filter options
 */
export interface ProductFilterOptions {
  category?: string;
  priceRange?: { min: number; max: number };
  inStock?: boolean;
  isNew?: boolean;
  isSale?: boolean;
  isFeatured?: boolean;
  searchQuery?: string;
}
