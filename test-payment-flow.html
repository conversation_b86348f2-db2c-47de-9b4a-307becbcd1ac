<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-box {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Payment Flow Test</h1>
    
    <div class="test-box">
        <h3>📋 Test Results</h3>
        <div id="test-results"></div>
    </div>
    
    <div class="test-box">
        <h3>🔧 Actions</h3>
        <button onclick="testAPIConnection()">1. Test API Connection</button>
        <button onclick="testRazorpayOrderCreation()">2. Test Razorpay Order Creation</button>
        <button onclick="testStorageMechanism()">3. Test Storage Mechanism</button>
        <button onclick="simulatePaymentFlow()">4. Simulate Complete Flow</button>
        <button onclick="clearAll()">Clear All</button>
    </div>
    
    <div class="test-box">
        <h3>📋 Debug Log</h3>
        <div class="log" id="debug-log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateResults(test, status, details = '') {
            const resultsDiv = document.getElementById('test-results');
            const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'orange';
            resultsDiv.innerHTML += `<div><strong>${test}:</strong> <span style="color: ${statusColor}">${status}</span> ${details}</div>`;
        }

        function getAPIUrl() {
            const isDevelopment = window.location.hostname === 'localhost' || 
                                  window.location.hostname === '127.0.0.1' || 
                                  window.location.hostname.startsWith('192.168.');
            
            if (isDevelopment) {
                if (window.location.hostname.startsWith('192.168.')) {
                    return `http://${window.location.hostname}:3001`;
                } else {
                    return 'http://localhost:3001';
                }
            } else {
                return window.location.origin;
            }
        }

        async function testAPIConnection() {
            log('🔗 Testing API connection...');
            const apiUrl = getAPIUrl();
            
            try {
                const response = await fetch(`${apiUrl}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ API connection successful: ${JSON.stringify(data)}`, 'success');
                    updateResults('API Connection', 'PASS', `- ${apiUrl}`);
                } else {
                    log(`❌ API connection failed: ${response.status}`, 'error');
                    updateResults('API Connection', 'FAIL', `- Status: ${response.status}`);
                }
            } catch (error) {
                log(`❌ API connection error: ${error.message}`, 'error');
                updateResults('API Connection', 'FAIL', `- Error: ${error.message}`);
            }
        }

        async function testRazorpayOrderCreation() {
            log('💳 Testing Razorpay order creation...');
            const apiUrl = getAPIUrl();
            
            try {
                const response = await fetch(`${apiUrl}/api/razorpay/create-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: 100, // ₹1.00 for testing
                        currency: 'INR',
                        receipt: `test_${Date.now()}`,
                        notes: {
                            user_id: 'test_user',
                            test: true
                        }
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ Razorpay order created: ${data.data.id}`, 'success');
                    updateResults('Razorpay Order Creation', 'PASS', `- Order ID: ${data.data.id}`);
                } else {
                    log(`❌ Razorpay order creation failed: ${JSON.stringify(data)}`, 'error');
                    updateResults('Razorpay Order Creation', 'FAIL', `- Error: ${data.error || 'Unknown'}`);
                }
            } catch (error) {
                log(`❌ Razorpay order creation error: ${error.message}`, 'error');
                updateResults('Razorpay Order Creation', 'FAIL', `- Error: ${error.message}`);
            }
        }

        function testStorageMechanism() {
            log('💾 Testing storage mechanism...');
            
            try {
                // Test localStorage
                const testData = {
                    order: {
                        id: 'test_order_123',
                        user_id: 'test_user',
                        total_amount: 100
                    },
                    timestamp: Date.now(),
                    paymentMethod: 'Online Payment',
                    userEmail: '<EMAIL>'
                };

                localStorage.setItem('pendingOrder', JSON.stringify(testData));
                sessionStorage.setItem('pendingOrder', JSON.stringify(testData));

                // Verify storage
                const storedLocal = localStorage.getItem('pendingOrder');
                const storedSession = sessionStorage.getItem('pendingOrder');

                if (storedLocal && storedSession) {
                    log('✅ Storage mechanism working correctly', 'success');
                    updateResults('Storage Mechanism', 'PASS', '- localStorage & sessionStorage working');
                } else {
                    log('❌ Storage mechanism failed', 'error');
                    updateResults('Storage Mechanism', 'FAIL', '- Storage not working');
                }
            } catch (error) {
                log(`❌ Storage mechanism error: ${error.message}`, 'error');
                updateResults('Storage Mechanism', 'FAIL', `- Error: ${error.message}`);
            }
        }

        async function simulatePaymentFlow() {
            log('🚀 Simulating complete payment flow...');
            
            // Step 1: Store pending order
            log('Step 1: Storing pending order...');
            testStorageMechanism();
            
            // Step 2: Test API connection
            log('Step 2: Testing API connection...');
            await testAPIConnection();
            
            // Step 3: Create Razorpay order
            log('Step 3: Creating Razorpay order...');
            await testRazorpayOrderCreation();
            
            // Step 4: Check if everything is working
            const pendingOrder = localStorage.getItem('pendingOrder');
            if (pendingOrder) {
                log('✅ Complete flow simulation successful', 'success');
                updateResults('Complete Flow', 'PASS', '- All steps completed');
            } else {
                log('❌ Complete flow simulation failed', 'error');
                updateResults('Complete Flow', 'FAIL', '- Missing pending order');
            }
        }

        function clearAll() {
            localStorage.removeItem('pendingOrder');
            sessionStorage.removeItem('pendingOrder');
            document.getElementById('debug-log').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
            log('🧹 All data cleared');
        }

        // Auto-run basic tests on load
        window.onload = function() {
            log('🚀 Payment flow test page loaded');
            log(`📍 Current URL: ${window.location.href}`);
            log(`🌐 API URL: ${getAPIUrl()}`);
        };
    </script>
</body>
</html>
