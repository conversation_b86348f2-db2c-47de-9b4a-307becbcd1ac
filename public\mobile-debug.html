<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Debug - UPI Payment Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-box {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Mobile UPI Payment Debug</h1>
    
    <div class="debug-box">
        <h3>📱 Device Detection</h3>
        <div id="device-info"></div>
    </div>
    
    <div class="debug-box">
        <h3>🌐 Network Information</h3>
        <div id="network-info"></div>
    </div>
    
    <div class="debug-box">
        <h3>💾 Storage Check</h3>
        <div id="storage-info"></div>
        <button onclick="checkStorage()">Refresh Storage</button>
        <button onclick="clearStorage()">Clear All Storage</button>
    </div>
    
    <div class="debug-box">
        <h3>🔗 API Connectivity</h3>
        <div id="api-info"></div>
        <button onclick="testAPI()">Test API Connection</button>
    </div>
    
    <div class="debug-box">
        <h3>📋 Debug Log</h3>
        <div class="log" id="debug-log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateDeviceInfo() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                             window.innerWidth <= 768 || 
                             ('ontouchstart' in window) ||
                             (navigator.maxTouchPoints > 0);
            
            document.getElementById('device-info').innerHTML = `
                <strong>User Agent:</strong> ${navigator.userAgent}<br>
                <strong>Window Width:</strong> ${window.innerWidth}px<br>
                <strong>Touch Support:</strong> ${'ontouchstart' in window}<br>
                <strong>Max Touch Points:</strong> ${navigator.maxTouchPoints}<br>
                <strong>Is Mobile:</strong> <span style="color: ${isMobile ? 'green' : 'red'}">${isMobile}</span>
            `;
        }

        function updateNetworkInfo() {
            document.getElementById('network-info').innerHTML = `
                <strong>Hostname:</strong> ${window.location.hostname}<br>
                <strong>Port:</strong> ${window.location.port}<br>
                <strong>Protocol:</strong> ${window.location.protocol}<br>
                <strong>Full URL:</strong> ${window.location.href}<br>
                <strong>API URL:</strong> ${getAPIUrl()}
            `;
        }

        function getAPIUrl() {
            const isDevelopment = window.location.hostname === 'localhost' || 
                                  window.location.hostname === '127.0.0.1' || 
                                  window.location.hostname.startsWith('192.168.');
            
            if (isDevelopment) {
                if (window.location.hostname.startsWith('192.168.')) {
                    return `http://${window.location.hostname}:3001`;
                } else {
                    return 'http://localhost:3001';
                }
            } else {
                return window.location.origin;
            }
        }

        function checkStorage() {
            const pendingOrder = localStorage.getItem('pendingOrder');
            const sessionPending = sessionStorage.getItem('pendingOrder');
            const paymentSuccess = localStorage.getItem('paymentSuccess');
            
            document.getElementById('storage-info').innerHTML = `
                <strong>localStorage pendingOrder:</strong> ${pendingOrder ? 'EXISTS' : 'NONE'}<br>
                <strong>sessionStorage pendingOrder:</strong> ${sessionPending ? 'EXISTS' : 'NONE'}<br>
                <strong>localStorage paymentSuccess:</strong> ${paymentSuccess ? 'EXISTS' : 'NONE'}<br>
                ${pendingOrder ? `<br><strong>Pending Order Data:</strong><br><pre style="font-size: 10px; background: #f0f0f0; padding: 5px;">${JSON.stringify(JSON.parse(pendingOrder), null, 2)}</pre>` : ''}
            `;
        }

        function clearStorage() {
            localStorage.removeItem('pendingOrder');
            sessionStorage.removeItem('pendingOrder');
            localStorage.removeItem('paymentSuccess');
            checkStorage();
            log('✅ All storage cleared');
        }

        async function testAPI() {
            const apiUrl = getAPIUrl();
            log(`🔗 Testing API connection to: ${apiUrl}`);
            
            try {
                const response = await fetch(`${apiUrl}/api/health`);
                const data = await response.json();
                
                document.getElementById('api-info').innerHTML = `
                    <div class="success">
                        <strong>✅ API Connection: SUCCESS</strong><br>
                        <strong>Status:</strong> ${data.status}<br>
                        <strong>Timestamp:</strong> ${data.timestamp}<br>
                        <strong>Razorpay Key:</strong> ${data.environment?.RAZORPAY_KEY_ID || 'MISSING'}
                    </div>
                `;
                log('✅ API connection successful');
            } catch (error) {
                document.getElementById('api-info').innerHTML = `
                    <div class="error">
                        <strong>❌ API Connection: FAILED</strong><br>
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
                log(`❌ API connection failed: ${error.message}`);
            }
        }

        // Initialize
        updateDeviceInfo();
        updateNetworkInfo();
        checkStorage();
        testAPI();
        
        log('🚀 Mobile debug page loaded');
        
        // Auto-refresh storage every 5 seconds
        setInterval(checkStorage, 5000);
    </script>
</body>
</html>
