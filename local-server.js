import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '.env.local') });

const app = express();
const PORT = process.env.VITE_SERVER_PORT || 3001;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:8080',
    'http://localhost:8081',
    'http://127.0.0.1:8080',
    'http://127.0.0.1:8081',
    // Allow network IP for mobile testing
    'http://*************:8080',
    'http://*************:8081',
    // Allow any 192.168.x.x network for mobile testing
    /^http:\/\/192\.168\.\d+\.\d+:(8080|8081)$/
  ],
  credentials: true
}));
app.use(express.json());

// Import API routes
import createOrderHandler from './api/razorpay/create-order.js';
import verifyPaymentHandler from './api/razorpay/verify-payment.js';
import webhookHandler from './api/razorpay/webhook.js';

// API Routes
app.post('/api/razorpay/create-order', createOrderHandler);
app.post('/api/razorpay/verify-payment', verifyPaymentHandler);
app.post('/api/razorpay/webhook', webhookHandler);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: {
      RAZORPAY_KEY_ID: process.env.RAZORPAY_KEY_ID ? process.env.RAZORPAY_KEY_ID.substring(0, 10) + '...' : 'MISSING',
      RAZORPAY_SECRET: process.env.RAZORPAY_SECRET ? 'SET' : 'MISSING'
    }
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Local API server running on http://localhost:${PORT}`);
  console.log(`🔧 Environment variables loaded from .env.local`);
  console.log(`🔑 Razorpay Key ID: ${process.env.RAZORPAY_KEY_ID ? process.env.RAZORPAY_KEY_ID.substring(0, 10) + '...' : 'MISSING'}`);
  console.log(`🔐 Razorpay Secret: ${process.env.RAZORPAY_SECRET ? 'SET' : 'MISSING'}`);
});
