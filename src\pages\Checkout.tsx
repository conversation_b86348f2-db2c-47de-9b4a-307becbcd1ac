import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

import { Loader2, ShoppingCart, MapPin, CreditCard, Check, Smartphone, CreditCard as CreditCardIcon } from 'lucide-react';
import { useCart } from '@/context/SupabaseCartContext';
import { useAuth } from '@/context/SupabaseAuthContext';
import { createOrder, Order, updateOrderWithPayment } from '@/services/orderService';
import { toast } from '@/hooks/use-toast';
import CheckoutAddressStep from '@/components/checkout/CheckoutAddressStep';
import RazorpayCheckout from '@/components/payment/RazorpayCheckout';
import { usePayment } from '@/hooks/usePayment';
import { useCODEnabled } from '@/hooks/useSettings';
import CODDisabledModal from '@/components/payment/CODDisabledModal';
import UPIPaymentRecovery from '@/components/payment/UPIPaymentRecovery';
import { formatShippingFee, ShippingCalculation } from '@/services/shippingService';
import { supabase } from '@/lib/supabase';

// Define checkout steps (simplified)
enum CheckoutStep {
  PAYMENT_AND_ADDRESS = 0,
  CONFIRMATION = 1,
}

const Checkout = () => {
  const navigate = useNavigate();
  const { cartItems, subtotal, clearCart } = useCart();
  const { isAuthenticated, user } = useAuth();
  const [currentStep, setCurrentStep] = useState<CheckoutStep>(CheckoutStep.PAYMENT_AND_ADDRESS);
  const [isLoading, setIsLoading] = useState(false);
  const [shippingAddress, setShippingAddress] = useState<any>(null);
  const [billingAddress, setBillingAddress] = useState<any>(null);
  const [paymentMethod, setPaymentMethod] = useState<string>('Cash on Delivery');
  const [orderId, setOrderId] = useState<string | null>(null);
  const [createdOrder, setCreatedOrder] = useState<Order | null>(null);
  const [showCODDisabledModal, setShowCODDisabledModal] = useState(false);
  const [showUPIRecovery, setShowUPIRecovery] = useState(false);

  // Shipping will be calculated in cart page
  const [shippingCalculation, setShippingCalculation] = useState<ShippingCalculation | null>(null);

  // Get COD availability from settings
  const { data: isCODEnabled, isLoading: isLoadingCOD } = useCODEnabled();

  // Update default payment method when COD availability changes
  useEffect(() => {
    if (!isLoadingCOD && isCODEnabled === false && paymentMethod === 'Cash on Delivery') {
      setPaymentMethod('Online Payment');
    }
  }, [isCODEnabled, isLoadingCOD, paymentMethod]);

  // Initialize payment hook
  const { processPayment, isLoading: isPaymentLoading } = usePayment({
    onSuccess: async (paymentId) => {
      // Payment successful - now create the order
      try {
        console.log('Payment successful, creating order with payment ID:', paymentId);

        // Calculate total including shipping
        const shippingFee = shippingCalculation?.shippingFee || 0;
        const totalAmount = subtotal + shippingFee;

        // Get saved shipping info from cart
        const savedShipping = sessionStorage.getItem('checkoutShipping');
        const isBangaloreDelivery = savedShipping ? JSON.parse(savedShipping).isBangaloreDelivery : null;

        // Create the order in the database after successful payment
        const newOrder = await createOrder(
          user.id,
          cartItems,
          totalAmount,
          paymentMethod,
          shippingAddress,
          billingAddress,
          {
            shippingFee,
            isBangaloreDelivery,
            shippingNotes: shippingCalculation?.notes.join('; ') || '',
          }
        );

        if (newOrder) {
          console.log('Order created successfully:', newOrder.id);

          // Update order with payment details
          const updateSuccess = await updateOrderWithPayment(newOrder.id, paymentId);

          if (updateSuccess) {
            console.log('Order updated with payment ID successfully');
          } else {
            console.warn('Failed to update order with payment ID, but order was created');
          }

          // Store order for confirmation
          setOrderId(newOrder.id);
          setCreatedOrder(newOrder);

          // Move to confirmation step
          setCurrentStep(CheckoutStep.CONFIRMATION);

          // Clear the cart after successful payment - with delay for mobile
          console.log('🛒 Clearing cart after successful payment');
          setTimeout(() => {
            clearCart(false);
            console.log('🛒 Cart cleared successfully');

            // Force a page refresh on mobile to ensure cart state is properly updated
            if (window.innerWidth <= 768) {
              console.log('📱 Mobile detected - forcing cart state refresh');
              window.dispatchEvent(new CustomEvent('cart-cleared'));
            }
          }, 100); // Small delay to ensure state updates properly on mobile

          toast({
            title: 'Payment Successful',
            description: 'Your payment was successful and your order has been placed!',
          });
        } else {
          throw new Error('Failed to create order after payment');
        }
      } catch (error) {
        console.error('Error creating order after payment:', error);
        toast({
          title: 'Order Creation Failed',
          description: `Payment was successful (ID: ${paymentId}) but order creation failed. Please contact support with this payment ID.`,
          variant: 'destructive'
        });
      }
    },
    onFailure: (error) => {
      console.error('Payment failed:', error);

      // Extract useful error information
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      toast({
        title: 'Payment Failed',
        description: `Payment could not be processed: ${errorMessage}. Please try again or contact support.`,
        variant: 'destructive'
      });
      // Don't clear cart on payment failure
    }
  });

  // Redirect to cart if cart is empty
  useEffect(() => {
    if (cartItems.length === 0 && currentStep !== CheckoutStep.CONFIRMATION) {
      navigate('/cart');
    }
  }, [cartItems, navigate, currentStep]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/checkout');
    }
  }, [isAuthenticated, navigate]);

  // Mobile payment recovery mechanism
  useEffect(() => {
    const checkMobilePaymentRecovery = () => {
      const pendingOrder = localStorage.getItem('pendingOrder');
      if (pendingOrder && window.innerWidth <= 768) {
        try {
          const orderData = JSON.parse(pendingOrder);
          const timeDiff = Date.now() - orderData.timestamp;

          // If order is less than 10 minutes old, it might be a mobile payment recovery
          if (timeDiff < 10 * 60 * 1000) {
            console.log('📱 Mobile payment recovery detected');
            console.log('📱 Pending order data:', orderData);

            // Check if we're on the checkout page and have the same user
            if (user?.id === orderData.order.user_id) {
              console.log('📱 User matches, checking for payment completion');

              // Show a recovery message
              toast({
                title: 'Checking Payment Status',
                description: 'We detected a recent payment attempt. Checking status...',
              });

              // Clean up after showing message
              setTimeout(() => {
                localStorage.removeItem('pendingOrder');
              }, 5000);
            }
          } else {
            // Clean up old pending orders
            localStorage.removeItem('pendingOrder');
          }
        } catch (error) {
          console.error('Error parsing pending order:', error);
          localStorage.removeItem('pendingOrder');
        }
      }
    };

    // Check on mount and when user changes
    if (user) {
      checkMobilePaymentRecovery();
    }
  }, [user, toast]);

  // UPI Payment Recovery - Check for completed payments when returning from UPI apps
  useEffect(() => {
    const checkUPIPaymentRecovery = async () => {
      console.log('🔍 [UPI RECOVERY] Starting UPI recovery check...');

      // Check for pending order first - this is the key indicator
      const pendingOrder = localStorage.getItem('pendingOrder') || sessionStorage.getItem('pendingOrder');
      console.log('🔍 [UPI RECOVERY] Pending order found:', !!pendingOrder);

      if (pendingOrder) {
        console.log('🔍 [UPI RECOVERY] Found pending order, triggering recovery...');
        setShowUPIRecovery(true);
        return;
      }

      // Only run on mobile devices - improved detection
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       window.innerWidth <= 768 ||
                       ('ontouchstart' in window) ||
                       (navigator.maxTouchPoints > 0);

      console.log('📱 [UPI RECOVERY] Mobile detection:', {
        userAgent: navigator.userAgent,
        windowWidth: window.innerWidth,
        ontouchstart: 'ontouchstart' in window,
        maxTouchPoints: navigator.maxTouchPoints,
        isMobile,
        userId: user?.id,
        hostname: window.location.hostname
      });

      if (!isMobile || !user?.id) {
        console.log('📱 [UPI RECOVERY] Skipping - not mobile or no user');
        return;
      }

      console.log('📱 [UPI RECOVERY] Checking for UPI payment recovery...');

      // Check if we have pending order data
      const pendingOrder = localStorage.getItem('pendingOrder');
      if (!pendingOrder) return;

      try {
        const orderData = JSON.parse(pendingOrder);
        const timeDiff = Date.now() - orderData.timestamp;

        // Only check for recent orders (within 15 minutes)
        if (timeDiff > 15 * 60 * 1000) {
          console.log('📱 [UPI RECOVERY] Pending order too old, cleaning up');
          localStorage.removeItem('pendingOrder');
          return;
        }

        console.log('📱 [UPI RECOVERY] Found pending order:', orderData);

        // Check if this is the same user
        if (orderData.order.user_id !== user.id) {
          console.log('📱 [UPI RECOVERY] User mismatch, cleaning up');
          localStorage.removeItem('pendingOrder');
          return;
        }

        // Show UPI recovery component
        setShowUPIRecovery(true);
        return; // Exit early to show recovery component

        // Check if payment was successful by querying recent payments
        try {
          // Try to get recent payment records for this user
          const { data: recentPayments, error } = await supabase
            .from('razorpay_payments')
            .select('*')
            .eq('user_id', user.id)
            .eq('status', 'captured')
            .gte('created_at', new Date(orderData.timestamp - 60000).toISOString()) // 1 minute before order creation
            .order('created_at', { ascending: false })
            .limit(1);

          if (error) {
            console.error('📱 [UPI RECOVERY] Error checking payments:', error);
            throw error;
          }

          if (recentPayments && recentPayments.length > 0) {
            const payment = recentPayments[0];
            console.log('📱 [UPI RECOVERY] Found successful payment:', payment);

            // Check if we already have an order for this payment
            const { data: existingOrder, error: orderError } = await supabase
              .from('orders')
              .select('*')
              .eq('razorpay_payment_id', payment.razorpay_payment_id)
              .single();

            if (orderError && orderError.code !== 'PGRST116') { // PGRST116 = no rows found
              console.error('📱 [UPI RECOVERY] Error checking existing order:', orderError);
            }

            if (existingOrder) {
              console.log('📱 [UPI RECOVERY] Order already exists:', existingOrder.id);

              // Order already exists, just show success
              setOrderId(existingOrder.id);
              setCreatedOrder(existingOrder);
              setCurrentStep(CheckoutStep.CONFIRMATION);
              clearCart(false);

              toast({
                title: 'Payment Successful!',
                description: `Your order #${existingOrder.id.substring(0, 8)} has been placed successfully.`,
              });
            } else {
              console.log('📱 [UPI RECOVERY] Creating order for successful payment');

              // Create order for successful payment
              const shippingFee = orderData.order.shipping_fee || 0;
              const totalAmount = orderData.order.total_amount;

              const newOrder = await createOrder(
                user.id,
                cartItems.length > 0 ? cartItems : [], // Use current cart or empty if cleared
                totalAmount,
                'Online Payment',
                orderData.order.shipping_address,
                orderData.order.billing_address,
                {
                  shippingFee,
                  isBangaloreDelivery: orderData.order.is_bangalore_delivery,
                  shippingNotes: orderData.order.shipping_notes || '',
                }
              );

              if (newOrder) {
                console.log('📱 [UPI RECOVERY] Order created successfully:', newOrder.id);

                // Update with payment details
                await updateOrderWithPayment(newOrder.id, payment.razorpay_payment_id);

                // Show success
                setOrderId(newOrder.id);
                setCreatedOrder(newOrder);
                setCurrentStep(CheckoutStep.CONFIRMATION);
                clearCart(false);

                toast({
                  title: 'Payment Successful!',
                  description: `Your order #${newOrder.id.substring(0, 8)} has been placed successfully.`,
                });
              }
            }

            // Clean up
            localStorage.removeItem('pendingOrder');
            localStorage.removeItem('paymentSuccess');

          } else {
            console.log('📱 [UPI RECOVERY] No successful payment found yet');

            // If no payment found after 2 minutes, clean up
            if (timeDiff > 2 * 60 * 1000) {
              console.log('📱 [UPI RECOVERY] No payment found after 2 minutes, cleaning up');
              localStorage.removeItem('pendingOrder');

              toast({
                title: 'Payment Status Unknown',
                description: 'We could not verify your payment status. Please check your orders or contact support if payment was deducted.',
                variant: 'destructive'
              });
            }
          }

        } catch (error) {
          console.error('📱 [UPI RECOVERY] Error during recovery:', error);

          toast({
            title: 'Payment Check Failed',
            description: 'Unable to verify payment status. Please check your orders or contact support.',
            variant: 'destructive'
          });
        }

      } catch (error) {
        console.error('📱 [UPI RECOVERY] Error parsing pending order:', error);
        localStorage.removeItem('pendingOrder');
      }
    };

    // Run recovery check when component mounts and user is available
    if (user) {
      // Immediate check
      checkUPIPaymentRecovery();

      // Add a small delay to ensure all data is loaded
      const timer = setTimeout(checkUPIPaymentRecovery, 1000);

      // For testing: also run recovery check every 3 seconds if pending order exists
      const testingInterval = setInterval(() => {
        const pendingOrder = localStorage.getItem('pendingOrder') || sessionStorage.getItem('pendingOrder');
        if (pendingOrder) {
          console.log('📱 [TESTING] Found pending order, running recovery check...');
          checkUPIPaymentRecovery();
        }
      }, 3000);

      // Listen for page visibility changes (when user returns from UPI app)
      const handleVisibilityChange = () => {
        if (!document.hidden) {
          console.log('📱 [UPI RECOVERY] Page became visible, checking for pending payments...');
          setTimeout(checkUPIPaymentRecovery, 500);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        clearTimeout(timer);
        clearInterval(testingInterval);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }
  }, [user, cartItems, toast]);

  // Listen for page visibility changes (when user returns from UPI app)
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Only run on mobile when page becomes visible - improved detection
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       window.innerWidth <= 768 ||
                       ('ontouchstart' in window) ||
                       (navigator.maxTouchPoints > 0);

      if (isMobile && !document.hidden && user?.id) {
        console.log('📱 [VISIBILITY] Page became visible - checking for UPI payment completion');

        // Check if we have pending order
        const pendingOrder = localStorage.getItem('pendingOrder') || sessionStorage.getItem('pendingOrder');
        if (pendingOrder) {
          console.log('📱 [VISIBILITY] Found pending order, triggering recovery check');

          // Trigger recovery check after a short delay
          setTimeout(() => {
            // Re-trigger the UPI recovery check
            window.location.reload();
          }, 2000);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user]);

  // Load shipping calculation from cart page
  useEffect(() => {
    const savedShipping = sessionStorage.getItem('checkoutShipping');
    if (savedShipping) {
      try {
        const { calculation } = JSON.parse(savedShipping);
        setShippingCalculation(calculation);
      } catch (error) {
        console.error('Error parsing saved shipping data:', error);
        // Fallback calculation
        setShippingCalculation({
          shippingFee: 50,
          isFreeShipping: false,
          isManualCalculation: false,
          notes: ['Default shipping applied'],
          eligibleForFreeShipping: false,
        });
      }
    } else if (cartItems.length > 0) {
      // Fallback if no shipping data from cart
      setShippingCalculation({
        shippingFee: 50,
        isFreeShipping: false,
        isManualCalculation: false,
        notes: ['Please calculate shipping in cart first'],
        eligibleForFreeShipping: false,
      });
    }
  }, [cartItems]);

  // Handle address selection
  const handleAddressSelect = (shipping: any, billing: any) => {
    setShippingAddress(shipping);
    setBillingAddress(billing);
  };

  // Handle payment method selection
  const handlePaymentMethodSelect = (method: string) => {
    // If COD is selected but disabled, show modal
    if (method === 'Cash on Delivery' && !isCODEnabled) {
      setShowCODDisabledModal(true);
      return;
    }
    setPaymentMethod(method);
  };

  // Handle COD disabled modal actions
  const handleCODModalClose = () => {
    setShowCODDisabledModal(false);
  };

  const handleSelectOnlinePayment = () => {
    setPaymentMethod('Online Payment');
    setShowCODDisabledModal(false);
  };

  // Handle place order
  const handlePlaceOrder = async () => {
    if (!user || !shippingAddress || !billingAddress) return;

    setIsLoading(true);
    try {
      // If payment method is Online Payment, start payment process without creating order
      if (paymentMethod === 'Online Payment') {
        // Create a temporary order object for payment processing
        const shippingFee = shippingCalculation?.shippingFee || 0;
        const totalAmount = subtotal + shippingFee;

        const tempOrder: Order = {
          id: `temp_${Date.now()}`, // Temporary ID
          user_id: user.id,
          status: 'pending',
          payment_method: paymentMethod,
          total_amount: totalAmount,
          shipping_address: shippingAddress,
          billing_address: billingAddress || shippingAddress,
          shipping_fee: shippingFee,
          is_bangalore_delivery: shippingCalculation?.isBangaloreDelivery,
          shipping_notes: shippingCalculation?.notes.join('; ') || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          payment_status: 'pending',
          items: cartItems // Include cart items for recovery
        };

        console.log('🚀 [CHECKOUT] Creating temp order for payment:', tempOrder);
        console.log('🛒 [CHECKOUT] Cart items included:', cartItems.length);

        // Process payment - order will be created in payment success callback
        console.log('🚀 [CHECKOUT] Starting payment process...');
        try {
          await processPayment(tempOrder);
          console.log('✅ [CHECKOUT] Payment process initiated successfully');
        } catch (error) {
          console.error('❌ [CHECKOUT] Payment process failed:', error);
          toast({
            title: 'Payment Error',
            description: 'Failed to start payment process. Please try again.',
            variant: 'destructive'
          });
          setIsLoading(false);
          return;
        }

        toast({
          title: 'Processing Payment',
          description: 'Please complete the payment process.',
        });

        setIsLoading(false);
        return;
      }

      // For COD and other payment methods, create order immediately
      const shippingFee = shippingCalculation?.shippingFee || 0;
      const totalAmount = subtotal + shippingFee;

      // Get saved shipping info from cart
      const savedShipping = sessionStorage.getItem('checkoutShipping');
      const isBangaloreDelivery = savedShipping ? JSON.parse(savedShipping).isBangaloreDelivery : null;

      // Create the order in the database with shipping information
      const newOrder = await createOrder(
        user.id,
        cartItems,
        totalAmount,
        paymentMethod,
        shippingAddress,
        billingAddress,
        {
          shippingFee,
          isBangaloreDelivery,
          shippingNotes: shippingCalculation?.notes.join('; ') || '',
        }
      );

      if (newOrder) {
        // Store order ID for confirmation
        setOrderId(newOrder.id);
        setCreatedOrder(newOrder);

        // Clear the cart (without showing toast since we show order success)
        clearCart(false);

        // Move to confirmation step
        setCurrentStep(CheckoutStep.CONFIRMATION);

        toast({
          title: 'Order Placed',
          description: 'Your order has been placed successfully!',
        });
      }
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: 'Error',
        description: 'Failed to place your order. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Render step indicator (simplified)
  const renderStepIndicator = () => {
    return (
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            currentStep >= CheckoutStep.PAYMENT_AND_ADDRESS ? 'bg-badhees-600 text-white' : 'bg-gray-200'
          }`}>
            <CreditCard className="h-4 w-4" />
          </div>
          <div className={`w-12 h-1 ${
            currentStep > CheckoutStep.PAYMENT_AND_ADDRESS ? 'bg-badhees-600' : 'bg-gray-200'
          }`} />
        </div>

        <div className="flex items-center justify-center w-8 h-8 rounded-full ${
          currentStep >= CheckoutStep.CONFIRMATION ? 'bg-badhees-600 text-white' : 'bg-gray-200'
        }">
          <Check className="h-4 w-4" />
        </div>
      </div>
    );
  };



  // Render confirmation step
  const renderConfirmation = () => {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <Check className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-badhees-800 mb-4">Order Placed Successfully!</h2>
        <p className="text-badhees-600 mb-6">
          Thank you for your order. Your order number is <span className="font-bold">#{orderId?.substring(0, 8)}</span>.
        </p>
        <p className="text-badhees-600 mb-8">
          We'll send you an email confirmation shortly.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4 mobile-stack-buttons">
          <Button
            variant="outline"
            onClick={() => navigate('/orders')}
            className="py-6 text-base"
            size="lg"
          >
            View Order History
          </Button>
          <Button
            onClick={() => navigate('/products')}
            className="py-6 text-base"
            size="lg"
          >
            Continue Shopping
          </Button>
        </div>
      </div>
    );
  };

  // Render combined payment and address step
  const renderPaymentAndAddress = () => {
    return (
      <div className="space-y-4 md:space-y-6">
        {/* Address Section */}
        <div className="space-y-3 md:space-y-4">
          <h2 className="text-lg md:text-xl font-bold">Shipping Address</h2>
          <CheckoutAddressStep
            onAddressSelect={handleAddressSelect}
            onContinue={() => {}} // No-op since we're handling the continue action in this component
            onBack={() => navigate('/cart')}
          />
        </div>

        <div className="border-t border-badhees-100 pt-4 md:pt-6 mt-2 md:mt-4"></div>

        {/* Payment Section */}
        <div className="space-y-3 md:space-y-4">
          <h2 className="text-lg md:text-xl font-bold">Payment Method</h2>

          <div className="space-y-3">
            <div
              className={`border rounded-lg overflow-hidden ${
                paymentMethod === 'Cash on Delivery' ? 'border-gray-300' : 'border-gray-200'
              }`}
            >
              <div
                className="flex items-center justify-between w-full py-4 px-5 cursor-pointer bg-white hover:bg-gray-50 transition-colors"
                onClick={() => handlePaymentMethodSelect('Cash on Delivery')}
              >
                <div className="flex items-center text-base font-medium">
                  <ShoppingCart className="h-5 w-5 mr-3 text-gray-700" />
                  <span>Cash on Delivery</span>
                </div>
                {paymentMethod === 'Cash on Delivery' && isCODEnabled && (
                  <div className="text-badhees-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                  </div>
                )}
              </div>
            </div>

            <div
              className={`border rounded-lg overflow-hidden ${paymentMethod === 'Online Payment' ? 'border-gray-300' : 'border-gray-200'}`}
            >
              <div
                className={`flex items-center justify-between w-full py-4 px-5 cursor-pointer ${paymentMethod === 'Online Payment' ? 'bg-white' : 'bg-white'}`}
                onClick={() => handlePaymentMethodSelect('Online Payment')}
              >
                <div className="flex items-center text-base font-medium">
                  <CreditCardIcon className="h-5 w-5 mr-3 text-gray-700" />
                  <span>Online Payment</span>
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                    UPI, Cards
                  </span>
                </div>
                {paymentMethod === 'Online Payment' && (
                  <div className="text-badhees-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                  </div>
                )}
              </div>
            </div>

          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-badhees-50 p-3 rounded-lg shadow-sm">
          <h3 className="font-medium text-sm md:text-base mb-2">Order Summary</h3>
          <div className="flex justify-between mb-1.5 text-xs md:text-sm">
            <span>Subtotal:</span>
            <span>₹{subtotal.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
          </div>
          <div className="flex justify-between mb-1.5 text-xs md:text-sm">
            <span>Shipping:</span>
            <span>
              {shippingCalculation ? formatShippingFee(shippingCalculation) : 'Calculating...'}
            </span>
          </div>
          {shippingCalculation?.isManualCalculation && (
            <div className="text-xs text-amber-600 mb-1.5">
              * Shipping fee will be calculated separately
            </div>
          )}
          <Separator className="my-2" />
          <div className="flex justify-between font-bold text-sm md:text-base">
            <span>Total:</span>
            <span>
              ₹{(subtotal + (shippingCalculation?.shippingFee || 0)).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
              {shippingCalculation?.isManualCalculation && (
                <span className="text-xs font-normal text-amber-600 ml-1">+ shipping</span>
              )}
            </span>
          </div>
        </div>

        {/* Place Order Button */}
        <div className="pt-3 md:pt-4 sticky bottom-0 bg-white pb-3 md:pb-0 md:static">
          <Button
            onClick={handlePlaceOrder}
            disabled={isLoading || isPaymentLoading || !shippingAddress}
            className="w-full py-4 md:py-6 text-sm md:text-base shadow-md md:shadow-none"
            size="lg"
          >
            {isLoading || isPaymentLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 md:h-5 md:w-5 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                {paymentMethod === 'Online Payment' ? (
                  <>
                    <CreditCardIcon className="mr-2 h-4 w-4 md:h-5 md:w-5" />
                    Pay Now
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-4 w-4 md:h-5 md:w-5" />
                    Place Order
                  </>
                )}
              </>
            )}
          </Button>
        </div>
      </div>
    );
  };

  // Render current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case CheckoutStep.PAYMENT_AND_ADDRESS:
        return renderPaymentAndAddress();
      case CheckoutStep.CONFIRMATION:
        return renderConfirmation();
      default:
        return null;
    }
  };

  if (!isAuthenticated || (cartItems.length === 0 && currentStep !== CheckoutStep.CONFIRMATION)) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-badhees-600" />
        </div>
        <Footer />
      </div>
    );
  }

  // Show UPI recovery if needed
  if (showUPIRecovery) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow pt-20 md:pt-28 pb-12 md:pb-16">
          <div className="max-w-3xl mx-auto px-3 sm:px-6">
            <UPIPaymentRecovery
              onSuccess={(orderId) => {
                setOrderId(orderId);
                setCurrentStep(CheckoutStep.CONFIRMATION);
                setShowUPIRecovery(false);
              }}
              onFailure={() => {
                setShowUPIRecovery(false);
                toast({
                  title: 'Payment Recovery Failed',
                  description: 'Unable to recover your payment. Please try placing the order again.',
                  variant: 'destructive'
                });
              }}
            />
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-grow pt-20 md:pt-28 pb-12 md:pb-16">
        <div className="max-w-3xl mx-auto px-3 sm:px-6">
          {currentStep !== CheckoutStep.CONFIRMATION && renderStepIndicator()}
          {renderCurrentStep()}
        </div>
      </div>
      <Footer />

      {/* COD Disabled Modal */}
      <CODDisabledModal
        isOpen={showCODDisabledModal}
        onClose={handleCODModalClose}
        onSelectOnlinePayment={handleSelectOnlinePayment}
      />
    </div>
  );
};

export default Checkout;
