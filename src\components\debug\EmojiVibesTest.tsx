import React, { useState, useEffect } from 'react';
import EmojiReactions from '@/components/ui/EmojiReactions';
import { getInitialProducts } from '@/services/editorProductsService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button-system';
import { useAuth } from '@/context/SupabaseAuthContext';

const EmojiVibesTest: React.FC = () => {
  const [products, setProducts] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { isAuthenticated, user } = useAuth();

  useEffect(() => {
    const loadProducts = async () => {
      try {
        const productList = await getInitialProducts();
        setProducts(productList.slice(0, 5)); // Get first 5 products for testing
        if (productList.length > 0) {
          setSelectedProduct(productList[0]);
        }
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProducts();
  }, []);

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="text-center">Loading products for emoji vibes testing...</div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="p-8">
        <div className="text-center text-red-600">No products found for testing</div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-badhees-800 mb-4">Emoji Vibes System Test</h1>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h2 className="font-semibold text-blue-800 mb-2">Authentication Status</h2>
          <p className="text-blue-700">
            {isAuthenticated ? (
              <>✅ Authenticated as: {user?.email || 'Unknown user'}</>
            ) : (
              <>❌ Not authenticated - reactions will be view-only</>
            )}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Select Product for Testing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {products.map((product) => (
                <Button
                  key={product.id}
                  variant={selectedProduct?.id === product.id ? "primary" : "outline"}
                  onClick={() => setSelectedProduct(product)}
                  className="w-full justify-start text-left"
                >
                  {product.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Emoji Reactions Testing */}
        {selectedProduct && (
          <Card>
            <CardHeader>
              <CardTitle>Emoji Reactions for: {selectedProduct.name}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Interactive Mode (Product Detail Style) */}
              <div>
                <h3 className="font-semibold mb-2">Interactive Mode (Large)</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <EmojiReactions
                    productId={selectedProduct.id}
                    size="lg"
                    showCounts={true}
                    interactive={true}
                    layout="horizontal"
                  />
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {isAuthenticated ? 'Click emojis to react!' : 'Login to interact with reactions'}
                </p>
              </div>

              {/* Display Mode (Product Card Style) */}
              <div>
                <h3 className="font-semibold mb-2">Display Mode (Medium)</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <EmojiReactions
                    productId={selectedProduct.id}
                    size="md"
                    showCounts={true}
                    interactive={false}
                    layout="horizontal"
                  />
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  View-only mode (used in product grids)
                </p>
              </div>

              {/* Compact Mode (Small Cards) */}
              <div>
                <h3 className="font-semibold mb-2">Compact Mode (Small)</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <EmojiReactions
                    productId={selectedProduct.id}
                    size="sm"
                    showCounts={true}
                    interactive={false}
                    layout="compact"
                  />
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Compact layout for small product cards
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* All Products Grid */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-badhees-800 mb-6">All Products with Emoji Reactions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <Card key={product.id}>
              <CardContent className="p-4">
                <div className="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                  {product.image ? (
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <div className="text-gray-400">No Image</div>
                  )}
                </div>
                <h3 className="font-semibold text-badhees-800 mb-2 line-clamp-2">
                  {product.name}
                </h3>
                <div className="mb-3">
                  <EmojiReactions
                    productId={product.id}
                    size="sm"
                    showCounts={true}
                    interactive={false}
                    layout="compact"
                  />
                </div>
                <div className="text-lg font-bold text-badhees-accent">
                  ₹{product.price?.toLocaleString('en-IN') || 'N/A'}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-12 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h2 className="font-semibold text-yellow-800 mb-3">Testing Instructions</h2>
        <ul className="list-disc list-inside space-y-2 text-yellow-700">
          <li>If authenticated, try clicking different emojis in the interactive mode</li>
          <li>Click the same emoji twice to remove your reaction</li>
          <li>Open multiple browser tabs to test real-time updates</li>
          <li>Check that non-interactive modes don't respond to clicks</li>
          <li>Verify that counts update correctly across all components</li>
          <li>Test on mobile devices for touch responsiveness</li>
        </ul>
      </div>
    </div>
  );
};

export default EmojiVibesTest;
