import React, { useState, useEffect } from 'react';
import { X, Bug, Wifi, Database, Smartphone } from 'lucide-react';

interface DebugLog {
  timestamp: string;
  message: string;
  type: 'info' | 'success' | 'error' | 'warning';
}

const MobileDebugWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [logs, setLogs] = useState<DebugLog[]>([]);
  const [apiStatus, setApiStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [storageData, setStorageData] = useState<any>({});

  const addLog = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
    const newLog: DebugLog = {
      timestamp: new Date().toLocaleTimeString(),
      message,
      type
    };
    setLogs(prev => [...prev.slice(-19), newLog]); // Keep last 20 logs
    console.log(`[MOBILE DEBUG] ${message}`);
  };

  const checkAPI = async () => {
    try {
      const hostname = window.location.hostname;
      const apiUrl = hostname.startsWith('192.168.') 
        ? `http://${hostname}:3001` 
        : 'http://localhost:3001';
      
      const response = await fetch(`${apiUrl}/api/health`);
      if (response.ok) {
        setApiStatus('online');
        addLog('✅ API connection successful', 'success');
      } else {
        setApiStatus('offline');
        addLog('❌ API connection failed', 'error');
      }
    } catch (error) {
      setApiStatus('offline');
      addLog(`❌ API error: ${error.message}`, 'error');
    }
  };

  const checkStorage = () => {
    const pendingOrder = localStorage.getItem('pendingOrder');
    const sessionPending = sessionStorage.getItem('pendingOrder');
    const paymentSuccess = localStorage.getItem('paymentSuccess');
    
    setStorageData({
      pendingOrder: pendingOrder ? JSON.parse(pendingOrder) : null,
      sessionPending: sessionPending ? JSON.parse(sessionPending) : null,
      paymentSuccess: paymentSuccess ? JSON.parse(paymentSuccess) : null
    });
  };

  const clearStorage = () => {
    localStorage.removeItem('pendingOrder');
    sessionStorage.removeItem('pendingOrder');
    localStorage.removeItem('paymentSuccess');
    addLog('🧹 Storage cleared', 'warning');
    checkStorage();
  };

  useEffect(() => {
    // Initial checks
    checkAPI();
    checkStorage();
    addLog('🚀 Mobile debug widget loaded');

    // Auto-refresh
    const apiInterval = setInterval(checkAPI, 10000);
    const storageInterval = setInterval(checkStorage, 2000);

    // Listen for page visibility changes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        addLog('📱 Page became visible - checking for updates', 'warning');
        setTimeout(() => {
          checkAPI();
          checkStorage();
        }, 500);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'pendingOrder' || e.key === 'paymentSuccess') {
        addLog(`📦 Storage changed: ${e.key}`, 'warning');
        checkStorage();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      clearInterval(apiInterval);
      clearInterval(storageInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <>
      {/* Floating Debug Button */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-4 right-4 z-50 bg-red-600 text-white p-3 rounded-full shadow-lg hover:bg-red-700 transition-colors"
          style={{ zIndex: 9999 }}
        >
          <Bug className="h-6 w-6" />
        </button>
      )}

      {/* Debug Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-end" style={{ zIndex: 9999 }}>
          <div className="bg-white w-full max-h-[80vh] rounded-t-lg overflow-hidden">
            {/* Header */}
            <div className="bg-red-600 text-white p-3 flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Smartphone className="h-5 w-5" />
                <span className="font-bold">Mobile Debug</span>
              </div>
              <button onClick={() => setIsOpen(false)}>
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-3 overflow-y-auto max-h-[calc(80vh-60px)]">
              {/* Status Cards */}
              <div className="grid grid-cols-2 gap-2 mb-4">
                <div className={`p-2 rounded text-center text-sm ${
                  apiStatus === 'online' ? 'bg-green-100 text-green-800' : 
                  apiStatus === 'offline' ? 'bg-red-100 text-red-800' : 
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  <Wifi className="h-4 w-4 mx-auto mb-1" />
                  API: {apiStatus.toUpperCase()}
                </div>
                <div className={`p-2 rounded text-center text-sm ${
                  storageData.pendingOrder ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  <Database className="h-4 w-4 mx-auto mb-1" />
                  Storage: {storageData.pendingOrder ? 'HAS DATA' : 'EMPTY'}
                </div>
              </div>

              {/* Storage Info */}
              <div className="mb-4">
                <h3 className="font-bold text-sm mb-2">💾 Storage Status</h3>
                <div className="text-xs space-y-1">
                  <div>Pending Order: {storageData.pendingOrder ? '✅ EXISTS' : '❌ NONE'}</div>
                  <div>Session Storage: {storageData.sessionPending ? '✅ EXISTS' : '❌ NONE'}</div>
                  <div>Payment Success: {storageData.paymentSuccess ? '✅ EXISTS' : '❌ NONE'}</div>
                </div>
                {storageData.pendingOrder && (
                  <details className="mt-2">
                    <summary className="text-xs font-bold cursor-pointer">View Pending Order</summary>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                      {JSON.stringify(storageData.pendingOrder, null, 2)}
                    </pre>
                  </details>
                )}
              </div>

              {/* Actions */}
              <div className="mb-4">
                <h3 className="font-bold text-sm mb-2">🔧 Actions</h3>
                <div className="flex gap-2">
                  <button 
                    onClick={checkAPI}
                    className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                  >
                    Test API
                  </button>
                  <button 
                    onClick={clearStorage}
                    className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                  >
                    Clear Storage
                  </button>
                </div>
              </div>

              {/* Logs */}
              <div>
                <h3 className="font-bold text-sm mb-2">📋 Debug Logs</h3>
                <div className="bg-gray-100 p-2 rounded text-xs max-h-40 overflow-y-auto">
                  {logs.map((log, index) => (
                    <div key={index} className={`mb-1 ${
                      log.type === 'error' ? 'text-red-600' :
                      log.type === 'success' ? 'text-green-600' :
                      log.type === 'warning' ? 'text-orange-600' :
                      'text-gray-800'
                    }`}>
                      [{log.timestamp}] {log.message}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MobileDebugWidget;
