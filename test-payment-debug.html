<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Debug - Real Time</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-box {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: bold;
        }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔍 Real-Time Payment Debug</h1>
    
    <div class="debug-box">
        <h3>🌐 Connection Status</h3>
        <div id="connection-status"></div>
        <button onclick="testConnection()">Test Connection</button>
    </div>
    
    <div class="debug-box">
        <h3>💾 Storage Monitor</h3>
        <div id="storage-monitor"></div>
        <button onclick="clearStorage()">Clear Storage</button>
    </div>
    
    <div class="debug-box">
        <h3>🎯 Quick Actions</h3>
        <button onclick="openMainSite()">Open Main Site</button>
        <button onclick="openMobileDebug()">Open Mobile Debug</button>
        <button onclick="testPaymentAPI()">Test Payment API</button>
    </div>
    
    <div class="debug-box">
        <h3>📋 Real-Time Log</h3>
        <div class="log" id="debug-log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function getAPIUrl() {
            const hostname = window.location.hostname;
            if (hostname.startsWith('192.168.')) {
                return `http://${hostname}:3001`;
            } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'http://localhost:3001';
            } else {
                return window.location.origin;
            }
        }

        function updateConnectionStatus(status, details = '') {
            const statusDiv = document.getElementById('connection-status');
            const className = status === 'online' ? 'online' : 'offline';
            statusDiv.innerHTML = `<div class="status ${className}">API: ${status.toUpperCase()} ${details}</div>`;
        }

        function updateStorageMonitor() {
            const pendingOrder = localStorage.getItem('pendingOrder');
            const sessionPending = sessionStorage.getItem('pendingOrder');
            const paymentSuccess = localStorage.getItem('paymentSuccess');
            
            const storageDiv = document.getElementById('storage-monitor');
            storageDiv.innerHTML = `
                <div><strong>localStorage pendingOrder:</strong> ${pendingOrder ? '✅ EXISTS' : '❌ NONE'}</div>
                <div><strong>sessionStorage pendingOrder:</strong> ${sessionPending ? '✅ EXISTS' : '❌ NONE'}</div>
                <div><strong>localStorage paymentSuccess:</strong> ${paymentSuccess ? '✅ EXISTS' : '❌ NONE'}</div>
                <div><strong>Last Updated:</strong> ${new Date().toLocaleTimeString()}</div>
                ${pendingOrder ? `<details><summary>Pending Order Data</summary><pre style="font-size: 10px; background: #f0f0f0; padding: 5px; max-height: 100px; overflow-y: auto;">${JSON.stringify(JSON.parse(pendingOrder), null, 2)}</pre></details>` : ''}
            `;
        }

        async function testConnection() {
            log('🔗 Testing API connection...');
            const apiUrl = getAPIUrl();
            
            try {
                const response = await fetch(`${apiUrl}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ API connection successful: ${apiUrl}`, 'success');
                    updateConnectionStatus('online', `- ${apiUrl}`);
                } else {
                    log(`❌ API connection failed: ${response.status}`, 'error');
                    updateConnectionStatus('offline', `- Status: ${response.status}`);
                }
            } catch (error) {
                log(`❌ API connection error: ${error.message}`, 'error');
                updateConnectionStatus('offline', `- Error: ${error.message}`);
            }
        }

        async function testPaymentAPI() {
            log('💳 Testing payment API...');
            const apiUrl = getAPIUrl();
            
            try {
                const response = await fetch(`${apiUrl}/api/razorpay/create-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: 100,
                        currency: 'INR',
                        receipt: `test_${Date.now()}`,
                        notes: { test: true }
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ Payment API working: ${data.data.id}`, 'success');
                } else {
                    log(`❌ Payment API failed: ${JSON.stringify(data)}`, 'error');
                }
            } catch (error) {
                log(`❌ Payment API error: ${error.message}`, 'error');
            }
        }

        function openMainSite() {
            const url = `http://${window.location.hostname}:${window.location.port}/`;
            window.open(url, '_blank');
            log(`🌐 Opened main site: ${url}`);
        }

        function openMobileDebug() {
            const url = `http://${window.location.hostname}:${window.location.port}/mobile-debug.html`;
            window.open(url, '_blank');
            log(`📱 Opened mobile debug: ${url}`);
        }

        function clearStorage() {
            localStorage.removeItem('pendingOrder');
            sessionStorage.removeItem('pendingOrder');
            localStorage.removeItem('paymentSuccess');
            updateStorageMonitor();
            log('🧹 Storage cleared');
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // Auto-update storage monitor every 2 seconds
        setInterval(updateStorageMonitor, 2000);

        // Auto-test connection every 10 seconds
        setInterval(testConnection, 10000);

        // Initialize
        window.onload = function() {
            log('🚀 Real-time payment debug loaded');
            log(`📍 Current URL: ${window.location.href}`);
            log(`🌐 API URL: ${getAPIUrl()}`);
            
            testConnection();
            updateStorageMonitor();
        };

        // Listen for storage changes from other tabs
        window.addEventListener('storage', function(e) {
            if (e.key === 'pendingOrder' || e.key === 'paymentSuccess') {
                log(`📦 Storage changed: ${e.key} = ${e.newValue ? 'SET' : 'REMOVED'}`, 'warning');
                updateStorageMonitor();
            }
        });
    </script>
</body>
</html>
