const Razorpay = require('razorpay');
require('dotenv').config();

console.log('🔧 Testing Razorpay credentials...');
console.log('RAZORPAY_KEY_ID exists:', !!process.env.RAZORPAY_KEY_ID);
console.log('RAZORPAY_SECRET exists:', !!process.env.RAZORPAY_SECRET);

if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_SECRET) {
  console.error('❌ Missing Razorpay environment variables');
  process.exit(1);
}

const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_SECRET,
});

console.log('✅ Razorpay initialized successfully');

// Test creating an order
async function testCreateOrder() {
  try {
    const options = {
      amount: 100, // 1 rupee in paise
      currency: 'INR',
      receipt: 'test_receipt_' + Date.now(),
      notes: {
        test: true
      }
    };

    console.log('🔧 Creating test order with options:', options);
    const order = await razorpay.orders.create(options);
    console.log('✅ Test order created successfully:', order.id);
    console.log('📋 Order details:', order);
  } catch (error) {
    console.error('❌ Error creating test order:', error);
    console.error('❌ Error details:', {
      message: error.message,
      code: error.code,
      description: error.description,
      source: error.source,
      step: error.step,
      reason: error.reason
    });
  }
}

testCreateOrder();
