import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { createOrder, updateOrderWithPayment } from '@/services/orderService';
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface UPIPaymentRecoveryProps {
  onSuccess: (orderId: string) => void;
  onFailure: () => void;
}

const UPIPaymentRecovery: React.FC<UPIPaymentRecoveryProps> = ({
  onSuccess,
  onFailure
}) => {
  const { user } = useAuth();
  const { clearCart } = useCart();
  const [isChecking, setIsChecking] = useState(true);
  const [status, setStatus] = useState<'checking' | 'success' | 'failed' | 'not-found'>('checking');
  const [orderDetails, setOrderDetails] = useState<any>(null);

  useEffect(() => {
    const checkPaymentStatus = async () => {
      if (!user?.id) {
        setStatus('failed');
        setIsChecking(false);
        return;
      }

      try {
        console.log('🔍 [UPI RECOVERY] Starting payment status check...');
        
        // Get pending order data
        const pendingOrderData = localStorage.getItem('pendingOrder') || sessionStorage.getItem('pendingOrder');
        if (!pendingOrderData) {
          console.log('🔍 [UPI RECOVERY] No pending order found');
          setStatus('not-found');
          setIsChecking(false);
          return;
        }

        const orderData = JSON.parse(pendingOrderData);
        console.log('🔍 [UPI RECOVERY] Found pending order:', orderData);

        // Check for recent successful payments
        const { data: recentPayments, error: paymentError } = await supabase
          .from('razorpay_payments')
          .select('*')
          .eq('user_id', user.id)
          .in('status', ['captured', 'authorized'])
          .gte('created_at', new Date(orderData.timestamp - 60000).toISOString())
          .order('created_at', { ascending: false })
          .limit(3);

        if (paymentError) {
          console.error('🔍 [UPI RECOVERY] Error checking payments:', paymentError);
          throw paymentError;
        }

        console.log('🔍 [UPI RECOVERY] Recent payments:', recentPayments);

        if (recentPayments && recentPayments.length > 0) {
          // Found successful payment
          const payment = recentPayments[0];
          console.log('✅ [UPI RECOVERY] Found successful payment:', payment.razorpay_payment_id);

          // Check if order already exists
          const { data: existingOrder } = await supabase
            .from('orders')
            .select('*')
            .eq('razorpay_payment_id', payment.razorpay_payment_id)
            .single();

          if (existingOrder) {
            console.log('✅ [UPI RECOVERY] Order already exists:', existingOrder.id);
            setOrderDetails(existingOrder);
            setStatus('success');
            
            // Clean up and trigger success
            localStorage.removeItem('pendingOrder');
            sessionStorage.removeItem('pendingOrder');
            clearCart(false);
            onSuccess(existingOrder.id);
          } else {
            console.log('📦 [UPI RECOVERY] Creating new order for payment');
            
            // Create new order
            const newOrder = await createOrder(
              user.id,
              [], // Cart might be empty at this point
              orderData.order.total_amount,
              'Online Payment',
              orderData.order.shipping_address,
              orderData.order.billing_address,
              {
                shippingFee: orderData.order.shipping_fee || 0,
                isBangaloreDelivery: orderData.order.is_bangalore_delivery,
                shippingNotes: orderData.order.shipping_notes || '',
              }
            );

            if (newOrder) {
              console.log('✅ [UPI RECOVERY] Order created:', newOrder.id);
              
              // Update with payment details
              await updateOrderWithPayment(newOrder.id, payment.razorpay_payment_id);
              
              setOrderDetails(newOrder);
              setStatus('success');
              
              // Clean up and trigger success
              localStorage.removeItem('pendingOrder');
              sessionStorage.removeItem('pendingOrder');
              clearCart(false);
              onSuccess(newOrder.id);
            } else {
              throw new Error('Failed to create order');
            }
          }
        } else {
          console.log('❌ [UPI RECOVERY] No successful payment found');
          setStatus('not-found');
        }
      } catch (error) {
        console.error('❌ [UPI RECOVERY] Error during recovery:', error);
        setStatus('failed');
      } finally {
        setIsChecking(false);
      }
    };

    // Start checking after a short delay
    const timer = setTimeout(checkPaymentStatus, 2000);
    return () => clearTimeout(timer);
  }, [user, onSuccess, clearCart]);

  const handleRetry = () => {
    setIsChecking(true);
    setStatus('checking');
    
    // Retry after a delay
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleCancel = () => {
    // Clean up and go back
    localStorage.removeItem('pendingOrder');
    sessionStorage.removeItem('pendingOrder');
    onFailure();
  };

  if (isChecking) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4">
        <Loader2 className="h-12 w-12 animate-spin text-badhees-600 mb-4" />
        <h3 className="text-lg font-semibold text-badhees-800 mb-2">
          Checking Payment Status
        </h3>
        <p className="text-badhees-600 text-center">
          We're verifying your payment. This may take a few moments...
        </p>
      </div>
    );
  }

  if (status === 'success' && orderDetails) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4">
        <CheckCircle className="h-12 w-12 text-green-600 mb-4" />
        <h3 className="text-lg font-semibold text-badhees-800 mb-2">
          Payment Successful!
        </h3>
        <p className="text-badhees-600 text-center mb-4">
          Your order #{orderDetails.id.substring(0, 8)} has been placed successfully.
        </p>
      </div>
    );
  }

  if (status === 'failed') {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4">
        <XCircle className="h-12 w-12 text-red-600 mb-4" />
        <h3 className="text-lg font-semibold text-badhees-800 mb-2">
          Payment Verification Failed
        </h3>
        <p className="text-badhees-600 text-center mb-6">
          We couldn't verify your payment status. Please try again or contact support.
        </p>
        <div className="flex gap-4">
          <Button onClick={handleRetry} variant="default">
            Retry Check
          </Button>
          <Button onClick={handleCancel} variant="outline">
            Cancel
          </Button>
        </div>
      </div>
    );
  }

  if (status === 'not-found') {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4">
        <XCircle className="h-12 w-12 text-amber-600 mb-4" />
        <h3 className="text-lg font-semibold text-badhees-800 mb-2">
          No Recent Payment Found
        </h3>
        <p className="text-badhees-600 text-center mb-6">
          We couldn't find any recent successful payments. If you completed a payment, please wait a few minutes and try again.
        </p>
        <div className="flex gap-4">
          <Button onClick={handleRetry} variant="default">
            Check Again
          </Button>
          <Button onClick={handleCancel} variant="outline">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return null;
};

export default UPIPaymentRecovery;
