/**
 * Razorpay Payment Service
 *
 * This service handles Razorpay payment integration including:
 * - Creating orders
 * - Verifying payments
 * - Processing payment callbacks
 */
import Razorpay from 'razorpay';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

// Note: This service now uses API routes for server-side operations
// Frontend only needs the public key for Razorpay checkout
// Server-side operations (create order, verify payment) are handled by API routes

// Types
export interface RazorpayOrder {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  status: string;
  attempts: number;
  notes: any;
  created_at: number;
}

export interface RazorpayPayment {
  id: string;
  entity: string;
  amount: number;
  currency: string;
  status: string;
  order_id: string;
  method: string;
  created_at: number;
  captured: boolean;
}

export interface PaymentRecord {
  id?: string;
  order_id?: string; // Make optional since it might be null initially
  razorpay_order_id: string;
  razorpay_payment_id?: string;
  amount: number;
  currency: string;
  status: 'created' | 'attempted' | 'paid' | 'failed' | 'cancelled' | 'refunded';
  method?: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
  error_description?: string;
}

/**
 * Create a new Razorpay order
 *
 * @param amount Amount in smallest currency unit (paise for INR)
 * @param currency Currency code (default: INR)
 * @param receipt Your internal order ID or receipt number
 * @param notes Additional notes for the order
 * @returns Razorpay order object
 */
export const createRazorpayOrder = async (
  amount: number,
  currency: string = 'INR',
  receipt: string,
  notes: any = {}
): Promise<RazorpayOrder> => {
  try {
    // Try API route first (for production/Vercel)
    let response;
    let result;

    try {
      // Get the API base URL - use current origin for production, localhost:3001 for development
      const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      const apiBaseUrl = isDevelopment ? 'http://localhost:3001' : window.location.origin;

      console.log('🚀 Creating Razorpay order using API base URL:', apiBaseUrl);
      console.log('🔧 Development mode:', isDevelopment);

      response = await fetch(`${apiBaseUrl}/api/razorpay/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          receipt,
          notes,
        }),
      });

      console.log('📡 API Response status:', response.status);
      console.log('📡 API Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      result = await response.json();
      console.log('📦 API Response data:', result);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create Razorpay order');
      }

      console.log('✅ Razorpay order created successfully via API:', result.data.id);
    } catch (apiError) {
      console.error('❌ API route error:', apiError);

      // For development, we'll throw the error instead of using fallback
      // This helps identify and fix the actual issues
      throw new Error(`Failed to create Razorpay order: ${apiError.message}`);
    }

    const order = result.data;

    // Save order record to database
    await savePaymentRecord({
      order_id: null, // Will be updated when actual order is created
      razorpay_order_id: order.id,
      amount: amount,
      currency,
      status: 'created',
      user_id: notes.user_id || '',
    });

    return order;
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw new Error(`Failed to create payment order: ${error.message}`);
  }
};

/**
 * Verify Razorpay payment signature (now handled by API route)
 * This function is deprecated - use the /api/razorpay/verify-payment endpoint instead
 */

/**
 * Save payment record to database using secure function
 *
 * @param paymentRecord Payment record to save
 * @returns Saved payment record
 */
export const savePaymentRecord = async (paymentRecord: PaymentRecord): Promise<PaymentRecord> => {
  try {
    // Use the secure function to create payment record
    const { data, error } = await supabase.rpc('create_payment_record', {
      p_user_id: paymentRecord.user_id,
      p_razorpay_order_id: paymentRecord.razorpay_order_id,
      p_amount: paymentRecord.amount,
      p_order_id: paymentRecord.order_id || null,
      p_currency: paymentRecord.currency || 'INR',
      p_status: paymentRecord.status || 'created'
    });

    if (error) {
      console.error('Error saving payment record:', error);
      throw error;
    }

    // Return the created record by fetching it
    const { data: createdRecord, error: fetchError } = await supabase
      .from('razorpay_payments')
      .select('*')
      .eq('id', data)
      .single();

    if (fetchError) {
      console.error('Error fetching created payment record:', fetchError);
      throw fetchError;
    }

    return createdRecord;
  } catch (error) {
    console.error('Error saving payment record:', error);
    throw new Error(`Failed to save payment record: ${error.message}`);
  }
};

/**
 * Update payment record in database using secure function
 *
 * @param razorpayOrderId Razorpay order ID
 * @param updates Fields to update
 * @returns Updated payment record
 */
export const updatePaymentRecord = async (
  razorpayOrderId: string,
  updates: Partial<PaymentRecord>
): Promise<PaymentRecord> => {
  try {
    // Use the secure function to update payment record
    const { data, error } = await supabase.rpc('update_payment_record', {
      p_razorpay_order_id: razorpayOrderId,
      p_razorpay_payment_id: updates.razorpay_payment_id || null,
      p_status: updates.status || null,
      p_method: updates.method || null,
      p_error_description: updates.error_description || null
    });

    if (error) {
      console.error('Error updating payment record:', error);
      throw error;
    }

    // Return the updated record by fetching it
    const { data: updatedRecord, error: fetchError } = await supabase
      .from('razorpay_payments')
      .select('*')
      .eq('razorpay_order_id', razorpayOrderId)
      .single();

    if (fetchError) {
      console.error('Error fetching updated payment record:', fetchError);
      throw fetchError;
    }

    return updatedRecord;
  } catch (error) {
    console.error('Error updating payment record:', error);
    throw new Error(`Failed to update payment record: ${error.message}`);
  }
};

/**
 * Get payment record by Razorpay order ID
 *
 * @param razorpayOrderId Razorpay order ID
 * @returns Payment record
 */
export const getPaymentRecordByOrderId = async (razorpayOrderId: string): Promise<PaymentRecord> => {
  try {
    const { data, error } = await supabase
      .from('razorpay_payments')
      .select('*')
      .eq('razorpay_order_id', razorpayOrderId)
      .single();

    if (error) {
      console.error('Error fetching payment record:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error fetching payment record:', error);
    throw new Error(`Failed to fetch payment record: ${error.message}`);
  }
};
