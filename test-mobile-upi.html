<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile UPI Payment Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-box {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>📱 Mobile UPI Payment Test</h1>
    
    <div class="test-box">
        <h3>🔍 Current Status</h3>
        <div id="current-status"></div>
    </div>
    
    <div class="test-box">
        <h3>🧪 Test Actions</h3>
        <button onclick="simulatePaymentStart()">1. Simulate Payment Start</button>
        <button onclick="simulateUPIRedirect()">2. Simulate UPI App Redirect</button>
        <button onclick="simulateReturnFromUPI()">3. Simulate Return from UPI</button>
        <button onclick="checkRecoveryMechanism()">4. Check Recovery Mechanism</button>
        <button onclick="clearTestData()">Clear Test Data</button>
    </div>
    
    <div class="test-box">
        <h3>💾 Storage Status</h3>
        <div id="storage-status"></div>
        <button onclick="refreshStorage()">Refresh Storage</button>
    </div>
    
    <div class="test-box">
        <h3>📋 Debug Log</h3>
        <div class="log" id="debug-log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('current-status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'warning';
            statusDiv.innerHTML = `<div class="status ${className}">${message}</div>`;
        }

        function refreshStorage() {
            const pendingOrder = localStorage.getItem('pendingOrder');
            const sessionPending = sessionStorage.getItem('pendingOrder');
            const paymentSuccess = localStorage.getItem('paymentSuccess');
            
            const storageDiv = document.getElementById('storage-status');
            storageDiv.innerHTML = `
                <div><strong>localStorage pendingOrder:</strong> ${pendingOrder ? 'EXISTS' : 'NONE'}</div>
                <div><strong>sessionStorage pendingOrder:</strong> ${sessionPending ? 'EXISTS' : 'NONE'}</div>
                <div><strong>localStorage paymentSuccess:</strong> ${paymentSuccess ? 'EXISTS' : 'NONE'}</div>
                ${pendingOrder ? `<div><strong>Pending Order Data:</strong><br><pre style="font-size: 10px; background: #f0f0f0; padding: 5px; max-height: 100px; overflow-y: auto;">${JSON.stringify(JSON.parse(pendingOrder), null, 2)}</pre></div>` : ''}
            `;
        }

        function simulatePaymentStart() {
            log('🚀 Simulating payment start...');
            
            // Create test order data (similar to what the real app does)
            const orderData = {
                order: {
                    id: `temp_${Date.now()}`,
                    user_id: 'test_user_123',
                    total_amount: 1500,
                    shipping_address: {
                        name: 'Test User',
                        phone: '9876543210',
                        address: 'Test Address'
                    },
                    payment_status: 'pending'
                },
                timestamp: Date.now(),
                paymentMethod: 'Online Payment',
                userEmail: '<EMAIL>',
                isMobile: true
            };

            try {
                // Store in both localStorage and sessionStorage
                localStorage.setItem('pendingOrder', JSON.stringify(orderData));
                sessionStorage.setItem('pendingOrder', JSON.stringify(orderData));
                
                log('✅ Pending order stored successfully', 'success');
                updateStatus('Payment initiated - Pending order stored', 'success');
                refreshStorage();
                
                // Simulate Razorpay order creation
                setTimeout(() => {
                    log('💳 Simulating Razorpay order creation...');
                    const updatedData = {...orderData};
                    updatedData.order.razorpay_order_id = `order_${Date.now()}`;
                    
                    localStorage.setItem('pendingOrder', JSON.stringify(updatedData));
                    sessionStorage.setItem('pendingOrder', JSON.stringify(updatedData));
                    
                    log('✅ Razorpay order ID added to pending order', 'success');
                    refreshStorage();
                }, 1000);
                
            } catch (error) {
                log(`❌ Error storing pending order: ${error.message}`, 'error');
                updateStatus('Payment initiation failed', 'error');
            }
        }

        function simulateUPIRedirect() {
            log('📱 Simulating UPI app redirect...');
            
            const pendingOrder = localStorage.getItem('pendingOrder');
            if (!pendingOrder) {
                log('❌ No pending order found - run payment start first', 'error');
                updateStatus('No pending order - cannot simulate UPI redirect', 'error');
                return;
            }
            
            log('📱 User redirected to UPI app...');
            updateStatus('User in UPI app - completing payment...', 'warning');
            
            // Simulate payment completion in UPI app
            setTimeout(() => {
                log('💳 Simulating payment completion in UPI app...');
                
                // Simulate successful payment by storing payment success data
                const paymentData = {
                    razorpay_payment_id: `pay_${Date.now()}`,
                    razorpay_order_id: `order_${Date.now()}`,
                    razorpay_signature: `signature_${Date.now()}`,
                    timestamp: Date.now()
                };
                
                localStorage.setItem('paymentSuccess', JSON.stringify(paymentData));
                log('✅ Payment completed in UPI app', 'success');
                
                // Now simulate return to website
                setTimeout(() => {
                    simulateReturnFromUPI();
                }, 2000);
                
            }, 3000);
        }

        function simulateReturnFromUPI() {
            log('🔄 Simulating return from UPI app...');
            updateStatus('User returned from UPI app - checking payment status...', 'warning');
            
            // This is where the real issue happens - check if recovery mechanism works
            const pendingOrder = localStorage.getItem('pendingOrder');
            const paymentSuccess = localStorage.getItem('paymentSuccess');
            
            if (pendingOrder && paymentSuccess) {
                log('✅ Found both pending order and payment success data', 'success');
                log('🔍 Recovery mechanism should trigger now...');
                updateStatus('Recovery data found - should create order now', 'success');
                
                // Simulate order creation
                setTimeout(() => {
                    log('📦 Simulating order creation from recovery...');
                    const orderData = JSON.parse(pendingOrder);
                    const paymentData = JSON.parse(paymentSuccess);
                    
                    // Create final order
                    const finalOrder = {
                        ...orderData.order,
                        id: `order_${Date.now()}`,
                        razorpay_payment_id: paymentData.razorpay_payment_id,
                        payment_status: 'completed',
                        order_status: 'confirmed'
                    };
                    
                    log(`✅ Order created successfully: ${finalOrder.id}`, 'success');
                    updateStatus(`Order created successfully: ${finalOrder.id}`, 'success');
                    
                    // Clean up
                    localStorage.removeItem('pendingOrder');
                    localStorage.removeItem('paymentSuccess');
                    refreshStorage();
                    
                }, 2000);
                
            } else {
                log('❌ Missing recovery data', 'error');
                log(`Pending order: ${pendingOrder ? 'EXISTS' : 'MISSING'}`);
                log(`Payment success: ${paymentSuccess ? 'EXISTS' : 'MISSING'}`);
                updateStatus('Recovery failed - missing data', 'error');
            }
            
            refreshStorage();
        }

        function checkRecoveryMechanism() {
            log('🔍 Checking recovery mechanism...');
            
            const pendingOrder = localStorage.getItem('pendingOrder');
            const sessionPending = sessionStorage.getItem('pendingOrder');
            const paymentSuccess = localStorage.getItem('paymentSuccess');
            
            log(`Pending order (localStorage): ${pendingOrder ? 'EXISTS' : 'MISSING'}`);
            log(`Pending order (sessionStorage): ${sessionPending ? 'EXISTS' : 'MISSING'}`);
            log(`Payment success: ${paymentSuccess ? 'EXISTS' : 'MISSING'}`);
            
            if (pendingOrder || sessionPending) {
                log('✅ Recovery mechanism should trigger', 'success');
                updateStatus('Recovery mechanism ready', 'success');
            } else {
                log('❌ No data for recovery mechanism', 'error');
                updateStatus('No recovery data available', 'error');
            }
            
            refreshStorage();
        }

        function clearTestData() {
            localStorage.removeItem('pendingOrder');
            sessionStorage.removeItem('pendingOrder');
            localStorage.removeItem('paymentSuccess');
            document.getElementById('debug-log').innerHTML = '';
            updateStatus('Test data cleared', 'warning');
            refreshStorage();
            log('🧹 All test data cleared');
        }

        // Auto-refresh storage every 2 seconds
        setInterval(refreshStorage, 2000);

        // Initialize
        window.onload = function() {
            log('🚀 Mobile UPI test page loaded');
            updateStatus('Ready for testing', 'warning');
            refreshStorage();
        };
    </script>
</body>
</html>
