
import { useState, useEffect, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Search, ArrowUpDown, Loader2, Calendar, Eye, Filter, RefreshCw } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Order, getAllOrders, updateOrderStatus } from '@/services/orderService';
import { toast } from '@/hooks/use-toast';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

const statusColors = {
  pending: 'bg-amber-100 text-amber-800',
  processing: 'bg-purple-100 text-purple-800',
  shipped: 'bg-blue-100 text-blue-800',
  delivered: 'bg-green-100 text-green-800',
  canceled: 'bg-red-100 text-red-800'
};

// Status icons
const statusIcons = {
  pending: <Loader2 className="h-4 w-4 mr-1" />,
  processing: <RefreshCw className="h-4 w-4 mr-1" />,
  shipped: <Calendar className="h-4 w-4 mr-1" />,
  delivered: <Calendar className="h-4 w-4 mr-1" />,
  canceled: <Calendar className="h-4 w-4 mr-1" />
};

const AdminOrders = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({ from: undefined, to: undefined });
  const [isDateFilterActive, setIsDateFilterActive] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [newStatus, setNewStatus] = useState<'pending' | 'processing' | 'shipped' | 'delivered' | 'canceled'>('pending');
  const [statusNote, setStatusNote] = useState('');
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  // Fetch orders from the database
  const fetchOrders = useCallback(async () => {
    setIsLoading(true);
    try {
      const status = statusFilter !== 'all' ? statusFilter : undefined;
      const data = await getAllOrders(status);
      setOrders(data);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to load orders. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [statusFilter]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Check if user has access to this page
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/orders');
    } else if (!isAdmin()) {
      navigate('/');
    } else {
      // Fetch orders from the database
      fetchOrders();
    }
  }, [isAuthenticated, isAdmin, navigate, fetchOrders]);

  // Listen for order updates (real-time refresh for admin)
  useEffect(() => {
    const handleOrderUpdate = (event: CustomEvent) => {
      console.log('🔄 [Admin] Order updated event received:', event.detail);
      // Refresh orders to show updated payment information
      setTimeout(() => {
        fetchOrders();
      }, 500); // Small delay to ensure database is updated
    };

    // Add event listener
    window.addEventListener('order-updated', handleOrderUpdate as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('order-updated', handleOrderUpdate as EventListener);
    };
  }, [fetchOrders]);

  const handleOrderClick = (orderId: string) => {
    // Navigate to the order details page
    navigate(`/admin/orders/${orderId}`);
  };

  const handleUpdateStatus = async (orderId: string, newStatus: 'pending' | 'processing' | 'shipped' | 'delivered' | 'canceled') => {
    try {
      // Update the order status in the database
      const success = await updateOrderStatus(orderId, newStatus);

      if (success) {
        // Refresh the orders list
        fetchOrders();
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update order status. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handleStatusDialogOpen = (orderId: string, currentStatus: 'pending' | 'processing' | 'shipped' | 'delivered' | 'canceled') => {
    setSelectedOrderId(orderId);
    setNewStatus(currentStatus);
    setStatusNote('');
    setIsStatusDialogOpen(true);
  };

  const handleStatusUpdate = async () => {
    if (!selectedOrderId) return;

    try {
      const success = await updateOrderStatus(selectedOrderId, newStatus);

      if (success) {
        // Close dialog
        setIsStatusDialogOpen(false);

        // Reset status note
        setStatusNote('');

        // Refresh orders
        fetchOrders();

        toast({
          title: 'Status updated',
          description: `Order status has been updated to ${newStatus}.`,
        });
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update order status. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const clearDateFilter = () => {
    setDateRange({ from: undefined, to: undefined });
    setIsDateFilterActive(false);
  };

  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort orders - optimized for performance
  const filteredOrders = orders.filter(order => {
    // Only perform search if there's a search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const idMatch = order.id.toLowerCase().includes(query);
      const nameMatch = (order.customer_name || '').toLowerCase().includes(query);
      if (!idMatch && !nameMatch) return false;
    }

    // Filter by status if not 'all'
    if (statusFilter !== 'all' && order.status !== statusFilter) {
      return false;
    }

    // Filter by date range if active
    if (isDateFilterActive && dateRange.from) {
      const orderDate = new Date(order.created_at);

      // If only from date is set
      if (!dateRange.to) {
        return orderDate >= dateRange.from;
      }

      // If both from and to dates are set
      return orderDate >= dateRange.from && orderDate <= dateRange.to;
    }

    return true;
  }).sort((a, b) => {
    if (sortField === 'total_amount') {
      return sortDirection === 'asc' ? a.total_amount - b.total_amount : b.total_amount - a.total_amount;
    } else if (sortField === 'created_at') {
      return sortDirection === 'asc'
        ? new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        : new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
    // Default string comparison for other fields
    return sortDirection === 'asc'
      ? String(a[sortField as keyof Order] || '').localeCompare(String(b[sortField as keyof Order] || ''))
      : String(b[sortField as keyof Order] || '').localeCompare(String(a[sortField as keyof Order] || ''));
  });

  if (!isAuthenticated || !isAdmin()) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Admin Access Required</h1>
            <p className="text-badhees-600 mb-6 text-center">
              You need to be logged in as an admin to access this page.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <a href="/login?redirect=/admin/orders">Login</a>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1">
        <Navbar />

        <div className="pt-28 pb-16 px-4 sm:px-8 max-w-[1400px] mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-badhees-800 mb-6">
            Orders Management
          </h1>

          {/* Filters and Search */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-badhees-400" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by order ID or customer name"
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="canceled">Canceled</SelectItem>
              </SelectContent>
            </Select>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  {isDateFilterActive ? (
                    <span>
                      {dateRange.from ? format(dateRange.from, 'PP') : ''}
                      {dateRange.to ? ` - ${format(dateRange.to, 'PP')}` : ''}
                    </span>
                  ) : (
                    <span>Date Range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="p-3">
                  <CalendarComponent
                    mode="range"
                    selected={{
                      from: dateRange.from,
                      to: dateRange.to,
                    }}
                    onSelect={(range) => {
                      setDateRange(range || { from: undefined, to: undefined });
                      setIsDateFilterActive(Boolean(range?.from));
                    }}
                    initialFocus
                  />
                  <div className="flex justify-between mt-4">
                    <Button variant="outline" size="sm" onClick={clearDateFilter}>
                      Clear
                    </Button>
                    <Button size="sm" onClick={() => setIsDateFilterActive(Boolean(dateRange.from))}>
                      Apply
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Button variant="outline" onClick={fetchOrders} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </div>

          {/* Orders Table */}
          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[120px]">
                    <button
                      type="button"
                      className="flex items-center"
                      onClick={() => toggleSort('id')}
                    >
                      Order ID
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </button>
                  </TableHead>
                  <TableHead>
                    <button
                      type="button"
                      className="flex items-center"
                      onClick={() => toggleSort('customer_name')}
                    >
                      Customer
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </button>
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment Method</TableHead>
                  <TableHead>Payment ID</TableHead>
                  <TableHead>
                    <button
                      type="button"
                      className="flex items-center"
                      onClick={() => toggleSort('total_amount')}
                    >
                      Total
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </button>
                  </TableHead>
                  <TableHead>
                    <button
                      type="button"
                      className="flex items-center"
                      onClick={() => toggleSort('created_at')}
                    >
                      Date
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </button>
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex justify-center items-center">
                        <Loader2 className="h-6 w-6 animate-spin text-badhees-600" />
                        <span className="ml-3 text-badhees-600">Loading orders...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredOrders.length > 0 ? (
                  filteredOrders.map((order) => (
                    <TableRow key={order.id} className="hover:bg-badhees-50">
                      <TableCell className="font-medium">
                        <Link to={`/admin/orders/${order.id}`} className="text-badhees-600 hover:underline">
                          {order.id.substring(0, 8)}
                        </Link>
                      </TableCell>
                      <TableCell>{order.customer_name || 'Unknown Customer'}</TableCell>
                      <TableCell>
                        <Badge className={`${statusColors[order.status]} flex w-fit items-center`}>
                          {statusIcons[order.status]}
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{order.payment_method}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {order.razorpay_payment_id && (
                            <div className="text-sm">
                              <span className="font-medium text-green-600">Payment ID:</span>
                              <br />
                              <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                                {order.razorpay_payment_id}
                              </span>
                            </div>
                          )}
                          {order.razorpay_order_id && (
                            <div className="text-sm">
                              <span className="font-medium text-blue-600">Order ID:</span>
                              <br />
                              <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                                {order.razorpay_order_id}
                              </span>
                            </div>
                          )}
                          {!order.razorpay_payment_id && !order.razorpay_order_id && (
                            <span className="text-gray-400 text-sm">
                              {order.payment_method === 'cod' ? 'COD Order' : 'No Payment ID'}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>₹{order.total_amount.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</TableCell>
                      <TableCell>{new Date(order.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOrderClick(order.id)}
                            className="flex items-center gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            View
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusDialogOpen(order.id, order.status as any)}
                            className="flex items-center gap-1"
                          >
                            <Filter className="h-4 w-4" />
                            Status
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-badhees-600">
                      No orders found matching your criteria.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        <Footer />
      </div>

      {/* Status Update Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Order Status</DialogTitle>
            <DialogDescription>
              Change the status of order #{selectedOrderId?.substring(0, 8)}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">New Status</label>
              <Select value={newStatus} onValueChange={(value) => setNewStatus(value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="canceled">Canceled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Status Note (Optional)</label>
              <Textarea
                placeholder="Add a note about this status change"
                value={statusNote}
                onChange={(e) => setStatusNote(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleStatusUpdate}>Update Status</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminOrders;
